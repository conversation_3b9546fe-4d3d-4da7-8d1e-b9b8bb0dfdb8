// 打地鼠游戏逻辑
class WhackAMoleGame {
    constructor() {
        this.score = 0;
        this.timeLeft = 60;
        this.combo = 0;
        this.maxCombo = 0;
        this.totalClicks = 0;
        this.successfulHits = 0;
        this.isGameRunning = false;
        this.isPaused = false;
        this.gameTimer = null;
        this.moleTimer = null;
        this.currentMole = null;
        this.currentDifficulty = 'easy';

        // 难度配置
        this.difficultySettings = {
            easy: {
                name: '简单',
                moleSpeed: 2000,
                moleShowTime: 1800,
                speedDecrease: 5,
                showTimeDecrease: 2,
                minSpeed: 1200,
                minShowTime: 1000,
                timeLimit: 60,
                baseScore: 10
            },
            normal: {
                name: '普通',
                moleSpeed: 1500,
                moleShowTime: 1200,
                speedDecrease: 8,
                showTimeDecrease: 3,
                minSpeed: 800,
                minShowTime: 700,
                timeLimit: 60,
                baseScore: 15
            },
            hard: {
                name: '困难',
                moleSpeed: 1000,
                moleShowTime: 800,
                speedDecrease: 10,
                showTimeDecrease: 4,
                minSpeed: 500,
                minShowTime: 400,
                timeLimit: 45,
                baseScore: 20
            },
            expert: {
                name: '专家',
                moleSpeed: 700,
                moleShowTime: 500,
                speedDecrease: 12,
                showTimeDecrease: 5,
                minSpeed: 300,
                minShowTime: 200,
                timeLimit: 30,
                baseScore: 25
            }
        };
        
        this.initializeElements();
        this.bindEvents();
        this.createSoundEffects();
        this.setDifficulty('easy');
    }

    initializeElements() {
        // 获取DOM元素
        this.scoreElement = document.getElementById('score');
        this.timeElement = document.getElementById('time');
        this.comboElement = document.getElementById('combo');
        this.startBtn = document.getElementById('startBtn');
        this.pauseBtn = document.getElementById('pauseBtn');
        this.resetBtn = document.getElementById('resetBtn');
        this.gameBoard = document.getElementById('gameBoard');
        this.gameOverModal = document.getElementById('gameOverModal');
        this.finalScoreElement = document.getElementById('finalScore');
        this.finalDifficultyElement = document.getElementById('finalDifficulty');
        this.maxComboElement = document.getElementById('maxCombo');
        this.accuracyElement = document.getElementById('accuracy');
        this.playAgainBtn = document.getElementById('playAgainBtn');
        this.closeModalBtn = document.getElementById('closeModalBtn');
        this.particlesContainer = document.getElementById('particlesContainer');
        
        this.holes = document.querySelectorAll('.hole');
        this.moles = document.querySelectorAll('.mole');
        this.difficultyBtns = document.querySelectorAll('.difficulty-btn');
    }

    bindEvents() {
        // 按钮事件
        this.startBtn.addEventListener('click', () => this.startGame());
        this.pauseBtn.addEventListener('click', () => this.togglePause());
        this.resetBtn.addEventListener('click', () => this.resetGame());
        this.playAgainBtn.addEventListener('click', () => this.playAgain());
        this.closeModalBtn.addEventListener('click', () => this.closeModal());

        // 难度选择事件
        this.difficultyBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                if (!this.isGameRunning) {
                    const difficulty = btn.dataset.difficulty;
                    this.setDifficulty(difficulty);
                }
            });
        });

        // 地鼠点击事件
        this.moles.forEach((mole, index) => {
            mole.addEventListener('click', () => this.hitMole(index));
        });

        // 洞穴点击事件（未命中）
        this.holes.forEach((hole, index) => {
            hole.addEventListener('click', (e) => {
                if (e.target === hole) {
                    this.missedClick();
                }
            });
        });

        // 键盘事件
        document.addEventListener('keydown', (e) => {
            if (e.code === 'Space') {
                e.preventDefault();
                if (!this.isGameRunning) {
                    this.startGame();
                } else {
                    this.togglePause();
                }
            }
            
            // 数字键选择难度
            if (!this.isGameRunning) {
                const difficulties = ['easy', 'normal', 'hard', 'expert'];
                const keyNum = parseInt(e.key);
                if (keyNum >= 1 && keyNum <= 4) {
                    this.setDifficulty(difficulties[keyNum - 1]);
                }
            }
        });
    }

    createSoundEffects() {
        // 创建音效（使用Web Audio API）
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        this.sounds = {
            hit: () => this.playTone(800, 0.1, 'square'),
            miss: () => this.playTone(200, 0.2, 'sawtooth'),
            gameOver: () => this.playTone(300, 0.5, 'triangle')
        };
    }

    playTone(frequency, duration, type = 'sine') {
        const oscillator = this.audioContext.createOscillator();
        const gainNode = this.audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(this.audioContext.destination);
        
        oscillator.frequency.value = frequency;
        oscillator.type = type;
        
        gainNode.gain.setValueAtTime(0.3, this.audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, this.audioContext.currentTime + duration);
        
        oscillator.start(this.audioContext.currentTime);
        oscillator.stop(this.audioContext.currentTime + duration);
    }

    setDifficulty(difficulty) {
        if (!this.difficultySettings[difficulty]) return;
        
        this.currentDifficulty = difficulty;
        const settings = this.difficultySettings[difficulty];
        
        // 更新难度按钮状态
        this.difficultyBtns.forEach(btn => {
            btn.classList.remove('active');
            if (btn.dataset.difficulty === difficulty) {
                btn.classList.add('active');
            }
        });
        
        // 更新时间显示
        this.timeLeft = settings.timeLimit;
        this.updateDisplay();
        
        console.log(`难度设置为: ${settings.name}`);
    }

    startGame() {
        const settings = this.difficultySettings[this.currentDifficulty];
        
        this.isGameRunning = true;
        this.isPaused = false;
        this.score = 0;
        this.timeLeft = settings.timeLimit;
        this.combo = 0;
        this.maxCombo = 0;
        this.totalClicks = 0;
        this.successfulHits = 0;
        
        // 设置当前难度的参数
        this.moleSpeed = settings.moleSpeed;
        this.moleShowTime = settings.moleShowTime;
        this.baseScore = settings.baseScore;
        
        this.updateDisplay();
        this.updateButtons();
        
        // 开始游戏计时器
        this.gameTimer = setInterval(() => {
            if (!this.isPaused) {
                this.timeLeft--;
                this.updateDisplay();
                
                if (this.timeLeft <= 0) {
                    this.endGame();
                }
            }
        }, 1000);
        
        // 开始地鼠出现
        this.spawnMole();
        
        console.log(`游戏开始 - 难度: ${settings.name}`);
    }

    togglePause() {
        this.isPaused = !this.isPaused;
        this.pauseBtn.textContent = this.isPaused ? '继续' : '暂停';
        
        if (this.isPaused) {
            this.hideMole();
        } else {
            this.spawnMole();
        }
    }

    resetGame() {
        this.isGameRunning = false;
        this.isPaused = false;
        
        if (this.gameTimer) {
            clearInterval(this.gameTimer);
            this.gameTimer = null;
        }
        
        if (this.moleTimer) {
            clearTimeout(this.moleTimer);
            this.moleTimer = null;
        }
        
        this.hideMole();
        this.score = 0;
        this.timeLeft = 60;
        this.combo = 0;
        this.maxCombo = 0;
        this.totalClicks = 0;
        this.successfulHits = 0;
        
        this.updateDisplay();
        this.updateButtons();
    }

    spawnMole() {
        if (!this.isGameRunning || this.isPaused) return;
        
        const settings = this.difficultySettings[this.currentDifficulty];
        
        // 隐藏当前地鼠
        this.hideMole();
        
        // 随机选择一个洞穴
        const randomHole = Math.floor(Math.random() * this.holes.length);
        this.currentMole = randomHole;
        
        // 显示地鼠
        const mole = this.moles[randomHole];
        mole.classList.add('show');
        
        // 设置地鼠自动隐藏
        setTimeout(() => {
            if (this.currentMole === randomHole) {
                this.hideMole();
                this.combo = 0; // 重置连击
                this.updateDisplay();
            }
        }, this.moleShowTime);
        
        // 设置下一个地鼠出现时间
        this.moleTimer = setTimeout(() => {
            this.spawnMole();
        }, this.moleSpeed);
        
        // 根据难度设置动态调整速度
        if (this.moleSpeed > settings.minSpeed) {
            this.moleSpeed -= settings.speedDecrease;
        }
        if (this.moleShowTime > settings.minShowTime) {
            this.moleShowTime -= settings.showTimeDecrease;
        }
    }

    hideMole() {
        this.moles.forEach(mole => {
            mole.classList.remove('show');
            mole.classList.remove('hit');
        });
        this.currentMole = null;
    }

    hitMole(index) {
        if (!this.isGameRunning || this.isPaused || this.currentMole !== index) return;

        this.totalClicks++;
        this.successfulHits++;

        // 播放击中音效
        this.sounds.hit();

        // 添加击中动画
        const mole = this.moles[index];
        mole.classList.add('hit');

        // 计算得分
        let points = this.baseScore || 10;
        this.combo++;

        // 连击奖励
        if (this.combo > 1) {
            points += this.combo * 2;
        }

        // 难度奖励
        const difficultyMultiplier = {
            easy: 1,
            normal: 1.2,
            hard: 1.5,
            expert: 2
        };
        points = Math.floor(points * difficultyMultiplier[this.currentDifficulty]);

        this.score += points;
        this.maxCombo = Math.max(this.maxCombo, this.combo);

        // 创建粒子效果
        this.createParticles(index);

        // 显示得分动画
        this.showScoreAnimation(index, points);

        // 隐藏地鼠
        this.hideMole();

        this.updateDisplay();

        // 立即生成下一个地鼠
        if (this.moleTimer) {
            clearTimeout(this.moleTimer);
        }
        this.moleTimer = setTimeout(() => {
            this.spawnMole();
        }, 300);
    }

    missedClick() {
        if (!this.isGameRunning || this.isPaused) return;

        this.totalClicks++;
        this.combo = 0; // 重置连击
        this.sounds.miss();
        this.updateDisplay();
    }

    createParticles(holeIndex) {
        const hole = this.holes[holeIndex];
        const rect = hole.getBoundingClientRect();
        const centerX = rect.left + rect.width / 2;
        const centerY = rect.top + rect.height / 2;

        for (let i = 0; i < 8; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            particle.style.left = centerX + 'px';
            particle.style.top = centerY + 'px';
            particle.style.background = `hsl(${Math.random() * 60 + 30}, 100%, 60%)`;

            const angle = (i / 8) * Math.PI * 2;
            const distance = 50 + Math.random() * 30;
            const finalX = centerX + Math.cos(angle) * distance;
            const finalY = centerY + Math.sin(angle) * distance;

            particle.style.setProperty('--final-x', finalX + 'px');
            particle.style.setProperty('--final-y', finalY + 'px');

            this.particlesContainer.appendChild(particle);

            setTimeout(() => {
                particle.remove();
            }, 1000);
        }
    }

    showScoreAnimation(holeIndex, points) {
        const hole = this.holes[holeIndex];
        const rect = hole.getBoundingClientRect();

        const scoreElement = document.createElement('div');
        scoreElement.textContent = `+${points}`;
        scoreElement.style.cssText = `
            position: fixed;
            left: ${rect.left + rect.width / 2}px;
            top: ${rect.top}px;
            color: #FFD700;
            font-size: 1.5rem;
            font-weight: bold;
            pointer-events: none;
            z-index: 1000;
            animation: scoreFloat 1s ease-out forwards;
            transform: translateX(-50%);
        `;

        document.body.appendChild(scoreElement);

        setTimeout(() => {
            scoreElement.remove();
        }, 1000);
    }

    endGame() {
        this.isGameRunning = false;

        if (this.gameTimer) {
            clearInterval(this.gameTimer);
            this.gameTimer = null;
        }

        if (this.moleTimer) {
            clearTimeout(this.moleTimer);
            this.moleTimer = null;
        }

        this.hideMole();
        this.sounds.gameOver();
        this.showGameOverModal();
        this.updateButtons();
    }

    showGameOverModal() {
        const accuracy = this.totalClicks > 0 ? Math.round((this.successfulHits / this.totalClicks) * 100) : 0;
        const difficultyName = this.difficultySettings[this.currentDifficulty].name;

        this.finalDifficultyElement.textContent = difficultyName;
        this.finalScoreElement.textContent = this.score;
        this.maxComboElement.textContent = this.maxCombo;
        this.accuracyElement.textContent = accuracy + '%';

        this.gameOverModal.classList.add('show');
    }

    closeModal() {
        this.gameOverModal.classList.remove('show');
    }

    playAgain() {
        this.closeModal();
        this.resetGame();
        setTimeout(() => {
            this.startGame();
        }, 100);
    }

    updateDisplay() {
        this.scoreElement.textContent = this.score;
        this.timeElement.textContent = this.timeLeft;
        this.comboElement.textContent = this.combo;

        // 连击特效
        if (this.combo > 3) {
            this.comboElement.style.color = '#FFD700';
            this.comboElement.style.textShadow = '0 0 10px #FFD700';
        } else {
            this.comboElement.style.color = '#fff';
            this.comboElement.style.textShadow = '0 2px 4px rgba(0, 0, 0, 0.3)';
        }
    }

    updateButtons() {
        this.startBtn.disabled = this.isGameRunning;
        this.pauseBtn.disabled = !this.isGameRunning;
        this.pauseBtn.textContent = this.isPaused ? '继续' : '暂停';

        // 游戏运行时禁用难度选择
        this.difficultyBtns.forEach(btn => {
            btn.style.pointerEvents = this.isGameRunning ? 'none' : 'auto';
            btn.style.opacity = this.isGameRunning ? '0.5' : '1';
        });
    }
}

// 添加得分动画CSS
const style = document.createElement('style');
style.textContent = `
    @keyframes scoreFloat {
        0% {
            opacity: 1;
            transform: translateX(-50%) translateY(0);
        }
        100% {
            opacity: 0;
            transform: translateX(-50%) translateY(-50px);
        }
    }
`;
document.head.appendChild(style);

// 初始化游戏
document.addEventListener('DOMContentLoaded', () => {
    new WhackAMoleGame();
});
