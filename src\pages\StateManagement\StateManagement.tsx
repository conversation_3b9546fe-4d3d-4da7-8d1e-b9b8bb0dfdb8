import React, { createContext, useContext, useReducer, useState } from 'react';
import './StateManagement.css';

// Context API 示例
interface User {
  id: number;
  name: string;
  email: string;
}

interface UserContextType {
  user: User | null;
  setUser: (user: User | null) => void;
}

const UserContext = createContext<UserContextType | undefined>(undefined);

// useReducer 示例
interface CounterState {
  count: number;
  history: number[];
}

type CounterAction = 
  | { type: 'INCREMENT' }
  | { type: 'DECREMENT' }
  | { type: 'RESET' }
  | { type: 'SET'; payload: number };

const counterReducer = (state: CounterState, action: CounterAction): CounterState => {
  switch (action.type) {
    case 'INCREMENT':
      return {
        count: state.count + 1,
        history: [...state.history, state.count + 1]
      };
    case 'DECREMENT':
      return {
        count: state.count - 1,
        history: [...state.history, state.count - 1]
      };
    case 'RESET':
      return {
        count: 0,
        history: [...state.history, 0]
      };
    case 'SET':
      return {
        count: action.payload,
        history: [...state.history, action.payload]
      };
    default:
      return state;
  }
};

const UserProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  
  return (
    <UserContext.Provider value={{ user, setUser }}>
      {children}
    </UserContext.Provider>
  );
};

const UserProfile: React.FC = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('UserProfile must be used within UserProvider');
  }
  
  const { user, setUser } = context;
  
  const handleLogin = () => {
    setUser({
      id: 1,
      name: '张三',
      email: '<EMAIL>'
    });
  };
  
  const handleLogout = () => {
    setUser(null);
  };
  
  return (
    <div className="user-profile">
      <h3>👤 用户状态管理 (Context API)</h3>
      {user ? (
        <div className="user-info">
          <p><strong>姓名:</strong> {user.name}</p>
          <p><strong>邮箱:</strong> {user.email}</p>
          <button onClick={handleLogout} className="demo-btn logout">
            退出登录
          </button>
        </div>
      ) : (
        <div className="login-section">
          <p>请登录以查看用户信息</p>
          <button onClick={handleLogin} className="demo-btn login">
            登录
          </button>
        </div>
      )}
    </div>
  );
};

const CounterWithReducer: React.FC = () => {
  const [state, dispatch] = useReducer(counterReducer, {
    count: 0,
    history: [0]
  });
  
  return (
    <div className="counter-reducer">
      <h3>🔢 计数器 (useReducer)</h3>
      <div className="counter-display">
        <span className="count-value">{state.count}</span>
      </div>
      <div className="counter-controls">
        <button 
          onClick={() => dispatch({ type: 'DECREMENT' })}
          className="demo-btn"
        >
          -1
        </button>
        <button 
          onClick={() => dispatch({ type: 'INCREMENT' })}
          className="demo-btn"
        >
          +1
        </button>
        <button 
          onClick={() => dispatch({ type: 'SET', payload: 10 })}
          className="demo-btn"
        >
          设为10
        </button>
        <button 
          onClick={() => dispatch({ type: 'RESET' })}
          className="demo-btn"
        >
          重置
        </button>
      </div>
      <div className="history">
        <h4>历史记录:</h4>
        <div className="history-list">
          {state.history.slice(-10).map((value, index) => (
            <span key={index} className="history-item">
              {value}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
};

const StateManagement: React.FC = () => {
  return (
    <UserProvider>
      <div className="state-management">
        <div className="page-header">
          <h2>🗃️ 状态管理</h2>
          <p>学习React中的状态管理模式和最佳实践</p>
        </div>

        <div className="management-grid">
          <div className="management-card">
            <UserProfile />
          </div>
          
          <div className="management-card">
            <CounterWithReducer />
          </div>
        </div>

        <div className="concepts-section">
          <h3>📚 状态管理概念</h3>
          <div className="concepts-grid">
            <div className="concept-card">
              <h4>Context API</h4>
              <p>React内置的状态共享解决方案，适合中小型应用的全局状态管理。</p>
              <ul>
                <li>避免prop drilling</li>
                <li>组件间状态共享</li>
                <li>主题、用户信息等全局数据</li>
              </ul>
            </div>
            
            <div className="concept-card">
              <h4>useReducer</h4>
              <p>处理复杂状态逻辑的Hook，类似于Redux的reducer模式。</p>
              <ul>
                <li>复杂状态更新逻辑</li>
                <li>状态变更的可预测性</li>
                <li>便于测试和调试</li>
              </ul>
            </div>
            
            <div className="concept-card">
              <h4>状态提升</h4>
              <p>将状态提升到最近的共同父组件，实现兄弟组件间的状态共享。</p>
              <ul>
                <li>兄弟组件通信</li>
                <li>单一数据源</li>
                <li>数据流向清晰</li>
              </ul>
            </div>
            
            <div className="concept-card">
              <h4>Redux/Zustand</h4>
              <p>第三方状态管理库，适合大型应用的复杂状态管理。</p>
              <ul>
                <li>时间旅行调试</li>
                <li>中间件支持</li>
                <li>开发者工具</li>
              </ul>
            </div>
          </div>
        </div>

        <div className="best-practices">
          <h3>💡 最佳实践</h3>
          <div className="practices-list">
            <div className="practice-item">
              <h4>🎯 选择合适的状态管理方案</h4>
              <p>根据应用规模和复杂度选择：本地状态 → Context API → Redux</p>
            </div>
            <div className="practice-item">
              <h4>📦 状态归一化</h4>
              <p>避免嵌套过深的状态结构，保持状态扁平化</p>
            </div>
            <div className="practice-item">
              <h4>🔄 不可变更新</h4>
              <p>始终返回新的状态对象，避免直接修改现有状态</p>
            </div>
            <div className="practice-item">
              <h4>🎨 关注点分离</h4>
              <p>将状态逻辑与UI逻辑分离，提高代码可维护性</p>
            </div>
          </div>
        </div>
      </div>
    </UserProvider>
  );
};

export default StateManagement;
