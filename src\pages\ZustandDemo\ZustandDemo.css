.zustand-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
}

.demo-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.demo-header p {
  font-size: 1.2rem;
  color: #7f8c8d;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 40px;
}

.intro-section {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.intro-section h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.intro-section p {
  color: #5a6c7d;
  line-height: 1.6;
  margin-bottom: 25px;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 20px;
}

.feature-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  transition: transform 0.3s ease;
  border-left: 4px solid #3498db;
}

.feature-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.feature-card h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.feature-card p {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

.demo-sections {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
}

.demo-section {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.demo-section h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.demo-section p {
  color: #5a6c7d;
  line-height: 1.6;
  margin-bottom: 20px;
}

.practice-areas {
  display: grid;
  gap: 15px;
}

.practice-card {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #e74c3c;
  transition: all 0.3s ease;
}

.practice-card:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.practice-card h4 {
  color: #2c3e50;
  margin-bottom: 8px;
  font-size: 1rem;
}

.practice-card p {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin-bottom: 10px;
}

.code-hint {
  background: #2c3e50;
  padding: 8px 12px;
  border-radius: 5px;
  font-family: 'Courier New', monospace;
}

.code-hint code {
  color: #e74c3c;
  font-size: 0.8rem;
}

.learning-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step {
  display: flex;
  align-items: flex-start;
  gap: 15px;
}

.step-number {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  flex-shrink: 0;
}

.step-content h4 {
  color: #2c3e50;
  margin-bottom: 5px;
  font-size: 1rem;
}

.step-content p {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

.step-content code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  color: #e74c3c;
  font-family: 'Courier New', monospace;
}

.getting-started {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.getting-started h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.4rem;
}

.getting-started p {
  color: #5a6c7d;
  line-height: 1.6;
  margin-bottom: 25px;
}

.example-structure {
  margin-bottom: 25px;
}

.example-structure h4 {
  color: #2c3e50;
  margin-bottom: 15px;
}

.file-tree {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
  font-family: 'Courier New', monospace;
}

.file-item {
  padding: 3px 0;
  color: #2c3e50;
}

.file-item.indent {
  margin-left: 20px;
  color: #7f8c8d;
}

.tips {
  background: #fff3cd;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #ffc107;
}

.tips h4 {
  color: #856404;
  margin-bottom: 15px;
}

.tips ul {
  margin: 0;
  padding-left: 20px;
}

.tips li {
  color: #856404;
  margin-bottom: 8px;
  line-height: 1.5;
}

/* 测试区域样式 */
.test-section {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  margin-top: 20px;
}

.test-section h3 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.5rem;
  text-align: center;
}

.test-section > p {
  color: #5a6c7d;
  text-align: center;
  margin-bottom: 30px;
  font-size: 1rem;
}

.components-container {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

.component-card {
  background: #f8f9fa;
  border: 2px solid #e9ecef;
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  transition: all 0.3s ease;
  position: relative;
}

.component-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  border-radius: 15px 15px 0 0;
}

.component-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  border-color: #3498db;
}

.component-card h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
  font-weight: 600;
}

.counter-display {
  margin: 20px 0;
}

.count-number {
  display: inline-block;
  font-size: 2.5rem;
  font-weight: bold;
  color: #3498db;
  background: white;
  padding: 15px 25px;
  border-radius: 10px;
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.2);
  min-width: 80px;
  border: 2px solid #e9ecef;
}

.button-group {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 0.85rem;
  min-width: 70px;
}

.btn-primary {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  box-shadow: 0 3px 10px rgba(52, 152, 219, 0.3);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
}

.btn-secondary {
  background: linear-gradient(135deg, #95a5a6 0%, #7f8c8d 100%);
  color: white;
  box-shadow: 0 3px 10px rgba(149, 165, 166, 0.3);
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(149, 165, 166, 0.4);
}

.btn-danger {
  background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  color: white;
  box-shadow: 0 3px 10px rgba(231, 76, 60, 0.3);
}

.btn-danger:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
}

.count-display {
  margin: 20px 0;
}

.count-display p {
  font-size: 1.1rem;
  margin-bottom: 10px;
  color: #2c3e50;
}

.count-display strong {
  color: #3498db;
  font-size: 1.4rem;
}

.count-status {
  font-size: 0.9rem !important;
  color: #7f8c8d !important;
  font-style: italic;
  margin-top: 10px;
}

.global-status {
  background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
  color: white;
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.global-status h4 {
  margin-bottom: 10px;
  font-size: 1.2rem;
}

.global-status p {
  margin: 0;
  opacity: 0.9;
}

/* 代码示例样式 */
.code-example {
  margin-bottom: 25px;
  background: #f8f9fa;
  border-radius: 10px;
  overflow: hidden;
  border-left: 4px solid #3498db;
}

.code-example h4 {
  background: #e9ecef;
  margin: 0;
  padding: 15px 20px;
  color: #2c3e50;
  font-size: 1rem;
  border-bottom: 1px solid #dee2e6;
}

.code-example pre {
  margin: 0;
  padding: 20px;
  background: #2c3e50;
  color: #ecf0f1;
  overflow-x: auto;
}

.code-example code {
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  line-height: 1.5;
}

/* 优势网格样式 */
.advantages-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.advantage-item {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  text-align: center;
  border-left: 4px solid #27ae60;
  transition: transform 0.3s ease;
}

.advantage-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.advantage-item h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1rem;
}

.advantage-item p {
  color: #7f8c8d;
  font-size: 0.9rem;
  margin: 0;
}

/* 技巧网格样式 */
.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.tip-item {
  background: #fff3cd;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #ffc107;
}

.tip-item h4 {
  color: #856404;
  margin-bottom: 10px;
  font-size: 1rem;
}

.tip-item p {
  color: #856404;
  font-size: 0.9rem;
  margin-bottom: 10px;
  line-height: 1.5;
}

.tip-item code {
  background: #f8f9fa;
  padding: 2px 6px;
  border-radius: 3px;
  color: #e74c3c;
  font-family: 'Courier New', monospace;
  font-size: 0.8rem;
  display: block;
  margin-top: 8px;
  padding: 8px;
  border-radius: 5px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .demo-sections {
    grid-template-columns: 1fr;
  }

  .features-grid {
    grid-template-columns: 1fr;
  }

  .demo-header h2 {
    font-size: 2rem;
  }

  .demo-section {
    padding: 20px;
  }

  .intro-section {
    padding: 20px;
  }

  .getting-started {
    padding: 20px;
  }

  .components-container {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .advantages-grid {
    grid-template-columns: 1fr;
  }

  .tips-grid {
    grid-template-columns: 1fr;
  }

  .count-number {
    font-size: 2rem;
    padding: 12px 20px;
  }

  .button-group {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 120px;
  }
}
