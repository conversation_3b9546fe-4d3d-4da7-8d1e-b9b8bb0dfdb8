import React, { useState, useEffect } from 'react';
import styles from './params.module.css';

interface SystemStats {
  cpuUsage: number;
  memoryUsage: number;
  diskUsage: number;
  networkSpeed: number;
  activeUsers: number;
  uptime: string;
}

const ParamsB = () => {
  const [stats, setStats] = useState<SystemStats>({
    cpuUsage: 0,
    memoryUsage: 0,
    diskUsage: 0,
    networkSpeed: 0,
    activeUsers: 0,
    uptime: '00:00:00'
  });

  const [currentTime, setCurrentTime] = useState(new Date());
  const [notifications, setNotifications] = useState(3);

  // 模拟实时数据更新
  useEffect(() => {
    const interval = setInterval(() => {
      setStats(prev => ({
        cpuUsage: Math.max(10, Math.min(90, prev.cpuUsage + (Math.random() - 0.5) * 10)),
        memoryUsage: Math.max(20, Math.min(85, prev.memoryUsage + (Math.random() - 0.5) * 8)),
        diskUsage: Math.max(30, Math.min(80, prev.diskUsage + (Math.random() - 0.5) * 5)),
        networkSpeed: Math.max(1, Math.min(100, prev.networkSpeed + (Math.random() - 0.5) * 20)),
        activeUsers: Math.max(1, Math.min(50, prev.activeUsers + Math.floor((Math.random() - 0.5) * 3))),
        uptime: formatUptime(Date.now())
      }));
    }, 2000);

    const timeInterval = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    // 初始化数据
    setStats({
      cpuUsage: 45,
      memoryUsage: 62,
      diskUsage: 58,
      networkSpeed: 25,
      activeUsers: 12,
      uptime: formatUptime(Date.now())
    });

    return () => {
      clearInterval(interval);
      clearInterval(timeInterval);
    };
  }, []);

  const formatUptime = (timestamp: number) => {
    const hours = Math.floor(timestamp / 3600000) % 24;
    const minutes = Math.floor(timestamp / 60000) % 60;
    const seconds = Math.floor(timestamp / 1000) % 60;
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleCardClick = (cardType: string) => {
    console.log(`点击了 ${cardType} 卡片`);
    // 这里可以添加具体的点击处理逻辑
  };

  const handleAction = (action: string) => {
    console.log(`执行操作: ${action}`);
    if (action === 'refresh') {
      // 刷新数据
      setStats(prev => ({
        ...prev,
        cpuUsage: Math.random() * 80 + 10,
        memoryUsage: Math.random() * 70 + 20,
        networkSpeed: Math.random() * 90 + 10
      }));
    } else if (action === 'clear-notifications') {
      setNotifications(0);
    }
  };

  return (
    <div className={styles.dashboardContainer}>
      {/* 仪表板头部 */}
      <div className={styles.dashboardHeader}>
        <h2 className={styles.dashboardTitle}>系统监控仪表板</h2>
      </div>

      {/* 仪表板内容 */}
      <div className={styles.dashboardContent}>
        {/* 系统状态卡片 */}
        <div className={styles.card} onClick={() => handleCardClick('system-status')}>
          <div className={styles.cardIcon}>🖥️</div>
          <h3 className={styles.cardTitle}>系统状态</h3>
          <p className={styles.cardDescription}>实时监控系统运行状态</p>
          <div className={styles.statsGrid}>
            <div className={styles.statItem}>
              <p className={styles.statLabel}>CPU使用率</p>
              <p className={styles.statValue}>{stats.cpuUsage.toFixed(1)}%</p>
            </div>
            <div className={styles.statItem}>
              <p className={styles.statLabel}>内存使用率</p>
              <p className={styles.statValue}>{stats.memoryUsage.toFixed(1)}%</p>
            </div>
          </div>
          <div className={styles.progressBar}>
            <div
              className={styles.progressFill}
              style={{ width: `${stats.cpuUsage}%` }}
            ></div>
          </div>
        </div>

        {/* 网络监控卡片 */}
        <div className={styles.card} onClick={() => handleCardClick('network')}>
          <div className={styles.cardIcon}>🌐</div>
          <h3 className={styles.cardTitle}>网络监控</h3>
          <p className={styles.cardDescription}>网络流量和连接状态</p>
          <p className={styles.cardValue}>{stats.networkSpeed.toFixed(1)} MB/s</p>
          <div className={styles.progressBar}>
            <div
              className={styles.progressFill}
              style={{ width: `${stats.networkSpeed}%` }}
            ></div>
          </div>
          <button
            className={styles.actionButton}
            onClick={(e) => {
              e.stopPropagation();
              handleAction('refresh');
            }}
          >
            刷新数据
          </button>
        </div>

        {/* 用户活动卡片 */}
        <div className={styles.card} onClick={() => handleCardClick('users')}>
          <div className={styles.cardIcon}>👥</div>
          <h3 className={styles.cardTitle}>用户活动</h3>
          <p className={styles.cardDescription}>当前在线用户统计</p>
          <p className={styles.cardValue}>{stats.activeUsers}</p>
          <div className={styles.statsGrid}>
            <div className={styles.statItem}>
              <p className={styles.statLabel}>今日访问</p>
              <p className={styles.statValue}>1,234</p>
            </div>
            <div className={styles.statItem}>
              <p className={styles.statLabel}>新用户</p>
              <p className={styles.statValue}>56</p>
            </div>
          </div>
        </div>

        {/* 存储空间卡片 */}
        <div className={styles.card} onClick={() => handleCardClick('storage')}>
          <div className={styles.cardIcon}>💾</div>
          <h3 className={styles.cardTitle}>存储空间</h3>
          <p className={styles.cardDescription}>磁盘使用情况监控</p>
          <p className={styles.cardValue}>{stats.diskUsage.toFixed(1)}%</p>
          <div className={styles.progressBar}>
            <div
              className={styles.progressFill}
              style={{
                width: `${stats.diskUsage}%`,
                background: stats.diskUsage > 80 ? 'linear-gradient(45deg, #ef4444, #dc2626)' : 'linear-gradient(45deg, #4ade80, #22c55e)'
              }}
            ></div>
          </div>
          <div style={{ marginTop: '10px', color: 'rgba(255, 255, 255, 0.8)', fontSize: '0.9rem' }}>
            可用空间: {(100 - stats.diskUsage).toFixed(1)}%
          </div>
        </div>

        {/* 系统时间卡片 */}
        <div className={styles.card} onClick={() => handleCardClick('time')}>
          <div className={styles.cardIcon}>⏰</div>
          <h3 className={styles.cardTitle}>系统时间</h3>
          <p className={styles.cardDescription}>当前系统时间和运行时长</p>
          <p className={styles.cardValue} style={{ fontSize: '1.5rem' }}>
            {currentTime.toLocaleTimeString('zh-CN')}
          </p>
          <div className={styles.statsGrid}>
            <div className={styles.statItem}>
              <p className={styles.statLabel}>运行时长</p>
              <p className={styles.statValue}>{stats.uptime}</p>
            </div>
            <div className={styles.statItem}>
              <p className={styles.statLabel}>时区</p>
              <p className={styles.statValue}>GMT+8</p>
            </div>
          </div>
        </div>

        {/* 通知中心卡片 */}
        <div className={styles.card} onClick={() => handleCardClick('notifications')}>
          <div className={styles.cardIcon}>🔔</div>
          <h3 className={styles.cardTitle}>通知中心</h3>
          <p className={styles.cardDescription}>系统通知和警报</p>
          <p className={styles.cardValue}>{notifications}</p>
          <div style={{ marginTop: '15px', color: 'rgba(255, 255, 255, 0.8)' }}>
            {notifications > 0 ? '有新的系统通知' : '暂无新通知'}
          </div>
          <button
            className={styles.actionButton}
            onClick={(e) => {
              e.stopPropagation();
              handleAction('clear-notifications');
            }}
            disabled={notifications === 0}
          >
            清除通知
          </button>
        </div>
      </div>
    </div>
  );
};

export default ParamsB;
