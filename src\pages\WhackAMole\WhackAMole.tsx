import React, { useState, useEffect, useRef } from 'react';
import './WhackAMole.css';

const WhackAMole: React.FC = () => {
  const [score, setScore] = useState(0);
  const [timeLeft, setTimeLeft] = useState(60);
  const [combo, setCombo] = useState(0);
  const [isGameRunning, setIsGameRunning] = useState(false);
  const [currentMole, setCurrentMole] = useState<number | null>(null);
  const [showGameOver, setShowGameOver] = useState(false);
  const [difficulty, setDifficulty] = useState('easy');

  const gameTimerRef = useRef<NodeJS.Timeout | null>(null);
  const moleTimerRef = useRef<NodeJS.Timeout | null>(null);

  const difficultySettings = {
    easy: { name: '简单', speed: 2000, showTime: 1500, timeLimit: 60 },
    normal: { name: '普通', speed: 1500, showTime: 1200, timeLimit: 60 },
    hard: { name: '困难', speed: 1000, showTime: 800, timeLimit: 45 },
    expert: { name: '专家', speed: 700, showTime: 500, timeLimit: 30 }
  };

  // 开始游戏
  const startGame = () => {
    const settings = difficultySettings[difficulty as keyof typeof difficultySettings];
    setIsGameRunning(true);
    setScore(0);
    setTimeLeft(settings.timeLimit);
    setCombo(0);
    setShowGameOver(false);
    
    // 游戏计时器
    gameTimerRef.current = setInterval(() => {
      setTimeLeft(prev => {
        if (prev <= 1) {
          endGame();
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
    
    // 开始生成地鼠
    spawnMole();
  };

  // 结束游戏
  const endGame = () => {
    setIsGameRunning(false);
    setCurrentMole(null);
    setShowGameOver(true);
    
    if (gameTimerRef.current) {
      clearInterval(gameTimerRef.current);
    }
    if (moleTimerRef.current) {
      clearTimeout(moleTimerRef.current);
    }
  };

  // 生成地鼠
  const spawnMole = () => {
    if (!isGameRunning) return;
    
    const settings = difficultySettings[difficulty as keyof typeof difficultySettings];
    const randomHole = Math.floor(Math.random() * 9);
    setCurrentMole(randomHole);
    
    // 地鼠自动消失
    setTimeout(() => {
      setCurrentMole(null);
      setCombo(0);
    }, settings.showTime);
    
    // 下一个地鼠
    moleTimerRef.current = setTimeout(spawnMole, settings.speed);
  };

  // 击中地鼠
  const hitMole = (index: number) => {
    if (currentMole !== index || !isGameRunning) return;
    
    const newCombo = combo + 1;
    const points = 10 + (newCombo > 1 ? newCombo * 2 : 0);
    
    setScore(prev => prev + points);
    setCombo(newCombo);
    setCurrentMole(null);
    
    // 立即生成下一个地鼠
    if (moleTimerRef.current) {
      clearTimeout(moleTimerRef.current);
    }
    setTimeout(spawnMole, 300);
  };

  // 重置游戏
  const resetGame = () => {
    setIsGameRunning(false);
    setScore(0);
    setTimeLeft(60);
    setCombo(0);
    setCurrentMole(null);
    setShowGameOver(false);
    
    if (gameTimerRef.current) {
      clearInterval(gameTimerRef.current);
    }
    if (moleTimerRef.current) {
      clearTimeout(moleTimerRef.current);
    }
  };

  // 清理定时器
  useEffect(() => {
    return () => {
      if (gameTimerRef.current) clearInterval(gameTimerRef.current);
      if (moleTimerRef.current) clearTimeout(moleTimerRef.current);
    };
  }, []);

  return (
    <div className="whack-a-mole-page">
      <div className="page-header">
        <h2>🔨 打地鼠游戏</h2>
        <p>一个精美的打地鼠小游戏，支持多种难度选择</p>
      </div>
      
      <div className="game-container-react">
        <header className="game-header-react">
          <h1 className="game-title-react">🔨 打地鼠大作战</h1>
          <div className="game-stats-react">
            <div className="stat-item-react">
              <span className="stat-label-react">得分</span>
              <span className="stat-value-react">{score}</span>
            </div>
            <div className="stat-item-react">
              <span className="stat-label-react">时间</span>
              <span className="stat-value-react">{timeLeft}</span>
            </div>
            <div className="stat-item-react">
              <span className="stat-label-react">连击</span>
              <span className={`stat-value-react ${combo > 3 ? 'combo-highlight' : ''}`}>
                {combo}
              </span>
            </div>
          </div>
        </header>

        <main className="game-area-react">
          <div className="game-board-react">
            {Array.from({ length: 9 }, (_, index) => (
              <div 
                key={index} 
                className="hole-react" 
                onClick={() => hitMole(index)}
              >
                <div 
                  className={`mole-react ${currentMole === index ? 'show' : ''}`}
                >
                  <div className="mole-body-react">
                    <div className="mole-face-react">
                      <div className="mole-eyes-react">
                        <div className="eye-react left"></div>
                        <div className="eye-react right"></div>
                      </div>
                      <div className="mole-nose-react"></div>
                      <div className="mole-mouth-react"></div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </main>

        <div className="difficulty-selector-react">
          <h3 className="difficulty-title-react">选择难度</h3>
          <div className="difficulty-buttons-react">
            {Object.entries(difficultySettings).map(([key, settings]) => (
              <button
                key={key}
                className={`difficulty-btn-react ${difficulty === key ? 'active' : ''} ${isGameRunning ? 'disabled' : ''}`}
                onClick={() => setDifficulty(key)}
                disabled={isGameRunning}
              >
                <div className="difficulty-icon-react">
                  {key === 'easy' && '🐌'}
                  {key === 'normal' && '🐰'}
                  {key === 'hard' && '🐆'}
                  {key === 'expert' && '🚀'}
                </div>
                <div className="difficulty-name-react">{settings.name}</div>
                <div className="difficulty-desc-react">
                  {key === 'easy' && '慢节奏，适合新手'}
                  {key === 'normal' && '标准速度，平衡体验'}
                  {key === 'hard' && '快节奏，高手挑战'}
                  {key === 'expert' && '极限速度，终极挑战'}
                </div>
              </button>
            ))}
          </div>
        </div>

        <div className="game-controls-react">
          <button 
            className="btn-react btn-primary-react" 
            onClick={startGame}
            disabled={isGameRunning}
          >
            开始游戏
          </button>
          <button 
            className="btn-react btn-secondary-react" 
            onClick={resetGame}
          >
            重置
          </button>
        </div>

        {showGameOver && (
          <div className="game-over-modal-react show">
            <div className="modal-content-react">
              <h2>🎉 游戏结束！</h2>
              <div className="final-stats-react">
                <p>游戏难度: <span>{difficultySettings[difficulty as keyof typeof difficultySettings].name}</span></p>
                <p>最终得分: <span>{score}</span></p>
                <p>最高连击: <span>{combo}</span></p>
              </div>
              <div className="modal-buttons-react">
                <button 
                  className="btn-react btn-primary-react" 
                  onClick={() => {
                    setShowGameOver(false);
                    resetGame();
                    setTimeout(startGame, 100);
                  }}
                >
                  再玩一次
                </button>
                <button 
                  className="btn-react btn-secondary-react" 
                  onClick={() => setShowGameOver(false)}
                >
                  关闭
                </button>
              </div>
            </div>
          </div>
        )}
      </div>

      <div className="game-info">
        <div className="info-card">
          <h3>🎮 游戏特色</h3>
          <ul>
            <li>四种难度等级选择</li>
            <li>精美的动画效果</li>
            <li>连击奖励机制</li>
            <li>React Hooks实现</li>
            <li>响应式设计</li>
          </ul>
        </div>

        <div className="info-card">
          <h3>🛠️ 技术实现</h3>
          <ul>
            <li>React + TypeScript</li>
            <li>CSS3动画和过渡</li>
            <li>React Hooks状态管理</li>
            <li>响应式设计</li>
            <li>现代化UI设计</li>
          </ul>
        </div>

        <div className="info-card">
          <h3>🎯 操作说明</h3>
          <ul>
            <li>选择游戏难度</li>
            <li>点击"开始游戏"</li>
            <li>快速点击出现的地鼠</li>
            <li>获得连击奖励</li>
            <li>挑战高分记录</li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default WhackAMole;
