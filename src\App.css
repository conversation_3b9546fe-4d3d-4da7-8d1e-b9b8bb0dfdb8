.App {
  height: 100vh;
  width: 100vw;
  margin: 0;
  padding: 0;
  overflow: hidden;
}

.App-logo {
  height: 40vmin;
  pointer-events: none;
}

@media (prefers-reduced-motion: no-preference) {
  .App-logo {
    animation: App-logo-spin infinite 20s linear;
  }
}

.App-header {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: row;
  margin: 0;
  padding: 0;
  font-size: calc(10px + 2vmin);
  color: white;
}
.App-component {
  flex: 1; /* 使每个组件占据相同的空间 */
  height: 100vh; /* 占满整个屏幕高度 */
  box-sizing: border-box; /* 包含内边距和边框在内的宽度计算 */
  display: flex; /* 添加flex布局 */
  flex-direction: column; /* 垂直布局 */
}
@keyframes App-logo-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
