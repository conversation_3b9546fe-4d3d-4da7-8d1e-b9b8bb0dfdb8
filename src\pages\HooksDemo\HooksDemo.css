.hooks-demo {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  transition: all 0.3s ease;
}

.hooks-demo.light {
  background: #f8f9fa;
  color: #2c3e50;
}

.hooks-demo.dark {
  background: #2c3e50;
  color: #ecf0f1;
}

.demo-header {
  text-align: center;
  margin-bottom: 40px;
  position: relative;
}

.demo-header h2 {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.demo-header p {
  font-size: 1.2rem;
  opacity: 0.8;
  margin-bottom: 20px;
}

.theme-toggle {
  position: absolute;
  top: 0;
  right: 0;
}

.theme-btn {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 25px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.theme-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.hooks-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.hook-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.hooks-demo.dark .hook-card {
  background: #34495e;
  color: #ecf0f1;
}

.hook-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.hook-card h3 {
  color: #3498db;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.demo-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.demo-input {
  padding: 12px 15px;
  border: 2px solid #e1e8ed;
  border-radius: 8px;
  font-size: 1rem;
  transition: all 0.3s ease;
  background: white;
}

.hooks-demo.dark .demo-input {
  background: #2c3e50;
  border-color: #4a5f7a;
  color: #ecf0f1;
}

.demo-input:focus {
  outline: none;
  border-color: #3498db;
  box-shadow: 0 0 10px rgba(52, 152, 219, 0.2);
}

.demo-btn {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.time-display {
  text-align: center;
}

.time {
  font-size: 2rem;
  font-weight: bold;
  color: #3498db;
  font-family: 'Courier New', monospace;
}

.counter-display {
  text-align: center;
  margin-bottom: 20px;
}

.count-value {
  font-size: 3rem;
  font-weight: bold;
  color: #e74c3c;
  display: block;
}

.counter-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
}

.counter-controls .demo-btn {
  min-width: 50px;
}

.todo-input {
  display: flex;
  gap: 10px;
}

.todo-input .demo-input {
  flex: 1;
}

.memo-value {
  background: #f8f9fa;
  padding: 10px;
  border-radius: 8px;
  text-align: center;
  font-weight: bold;
  color: #27ae60;
}

.hooks-demo.dark .memo-value {
  background: #2c3e50;
}

.todo-list {
  list-style: none;
  padding: 0;
  max-height: 200px;
  overflow-y: auto;
}

.todo-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: #f8f9fa;
  margin-bottom: 5px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.hooks-demo.dark .todo-item {
  background: #2c3e50;
}

.todo-item:hover {
  background: #e9ecef;
}

.hooks-demo.dark .todo-item:hover {
  background: #34495e;
}

.remove-btn {
  background: #e74c3c;
  color: white;
  border: none;
  width: 25px;
  height: 25px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 1.2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.remove-btn:hover {
  background: #c0392b;
  transform: scale(1.1);
}

.hooks-info {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.hooks-demo.dark .hooks-info {
  background: #34495e;
}

.hooks-info h3 {
  color: #3498db;
  margin-bottom: 20px;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.info-item {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  transition: all 0.3s ease;
}

.hooks-demo.dark .info-item {
  background: #2c3e50;
}

.info-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.info-item h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.hooks-demo.dark .info-item h4 {
  color: #ecf0f1;
}

.info-item p {
  font-size: 0.9rem;
  opacity: 0.8;
}

@media (max-width: 768px) {
  .hooks-grid {
    grid-template-columns: 1fr;
  }
  
  .demo-header h2 {
    font-size: 2rem;
  }
  
  .theme-toggle {
    position: static;
    margin-top: 20px;
  }
  
  .todo-input {
    flex-direction: column;
  }
  
  .counter-controls {
    flex-wrap: wrap;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
