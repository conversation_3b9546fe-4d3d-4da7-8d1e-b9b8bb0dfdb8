import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import './HooksDemo.css';

// 自定义Hook示例
const useCounter = (initialValue: number = 0) => {
  const [count, setCount] = useState(initialValue);
  
  const increment = useCallback(() => setCount(c => c + 1), []);
  const decrement = useCallback(() => setCount(c => c - 1), []);
  const reset = useCallback(() => setCount(initialValue), [initialValue]);
  
  return { count, increment, decrement, reset };
};

const useLocalStorage = (key: string, initialValue: any) => {
  const [storedValue, setStoredValue] = useState(() => {
    try {
      const item = window.localStorage.getItem(key);
      return item ? JSON.parse(item) : initialValue;
    } catch (error) {
      return initialValue;
    }
  });

  const setValue = (value: any) => {
    try {
      setStoredValue(value);
      window.localStorage.setItem(key, JSON.stringify(value));
    } catch (error) {
      console.error(error);
    }
  };

  return [storedValue, setValue];
};

const HooksDemo: React.FC = () => {
  // useState 示例
  const [name, setName] = useState('');
  const [todos, setTodos] = useState<string[]>([]);
  const [newTodo, setNewTodo] = useState('');
  
  // useEffect 示例
  const [time, setTime] = useState(new Date());
  
  // useRef 示例
  const inputRef = useRef<HTMLInputElement>(null);
  const renderCount = useRef(0);
  
  // 自定义Hook示例
  const { count, increment, decrement, reset } = useCounter(0);
  const [theme, setTheme] = useLocalStorage('theme', 'light');
  
  // useEffect - 定时器
  useEffect(() => {
    const timer = setInterval(() => {
      setTime(new Date());
    }, 1000);
    
    return () => clearInterval(timer);
  }, []);
  
  // useEffect - 渲染计数
  useEffect(() => {
    renderCount.current += 1;
  });
  
  // useCallback 示例
  const addTodo = useCallback(() => {
    if (newTodo.trim()) {
      setTodos(prev => [...prev, newTodo.trim()]);
      setNewTodo('');
    }
  }, [newTodo]);
  
  const removeTodo = useCallback((index: number) => {
    setTodos(prev => prev.filter((_, i) => i !== index));
  }, []);
  
  // useMemo 示例
  const expensiveValue = useMemo(() => {
    console.log('计算昂贵的值...');
    return todos.length * 2 + count;
  }, [todos.length, count]);
  
  const focusInput = () => {
    inputRef.current?.focus();
  };

  return (
    <div className={`hooks-demo ${theme}`}>
      <div className="demo-header">
        <h2>⚛️ React Hooks 实践</h2>
        <p>学习和实践各种React Hooks的使用</p>
        <div className="theme-toggle">
          <button 
            onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
            className="theme-btn"
          >
            {theme === 'light' ? '🌙' : '☀️'} 切换主题
          </button>
        </div>
      </div>

      <div className="hooks-grid">
        {/* useState 示例 */}
        <div className="hook-card">
          <h3>useState Hook</h3>
          <div className="demo-content">
            <input
              type="text"
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="输入你的名字"
              className="demo-input"
            />
            <p>你好, {name || '匿名用户'}!</p>
            <p>渲染次数: {renderCount.current}</p>
          </div>
        </div>

        {/* useEffect 示例 */}
        <div className="hook-card">
          <h3>useEffect Hook</h3>
          <div className="demo-content">
            <div className="time-display">
              <p>当前时间:</p>
              <p className="time">{time.toLocaleTimeString()}</p>
            </div>
          </div>
        </div>

        {/* useRef 示例 */}
        <div className="hook-card">
          <h3>useRef Hook</h3>
          <div className="demo-content">
            <input
              ref={inputRef}
              type="text"
              placeholder="点击按钮聚焦我"
              className="demo-input"
            />
            <button onClick={focusInput} className="demo-btn">
              聚焦输入框
            </button>
          </div>
        </div>

        {/* 自定义Hook - useCounter */}
        <div className="hook-card">
          <h3>自定义Hook - useCounter</h3>
          <div className="demo-content">
            <div className="counter-display">
              <span className="count-value">{count}</span>
            </div>
            <div className="counter-controls">
              <button onClick={decrement} className="demo-btn">-</button>
              <button onClick={increment} className="demo-btn">+</button>
              <button onClick={reset} className="demo-btn">重置</button>
            </div>
          </div>
        </div>

        {/* useCallback & useMemo 示例 */}
        <div className="hook-card">
          <h3>useCallback & useMemo</h3>
          <div className="demo-content">
            <div className="todo-input">
              <input
                type="text"
                value={newTodo}
                onChange={(e) => setNewTodo(e.target.value)}
                placeholder="添加待办事项"
                className="demo-input"
                onKeyPress={(e) => e.key === 'Enter' && addTodo()}
              />
              <button onClick={addTodo} className="demo-btn">添加</button>
            </div>
            <div className="memo-value">
              计算值 (useMemo): {expensiveValue}
            </div>
            <ul className="todo-list">
              {todos.map((todo, index) => (
                <li key={index} className="todo-item">
                  <span>{todo}</span>
                  <button 
                    onClick={() => removeTodo(index)}
                    className="remove-btn"
                  >
                    ×
                  </button>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* 自定义Hook - useLocalStorage */}
        <div className="hook-card">
          <h3>自定义Hook - useLocalStorage</h3>
          <div className="demo-content">
            <p>当前主题: {theme}</p>
            <p>数据已保存到 localStorage</p>
            <button 
              onClick={() => setTheme(theme === 'light' ? 'dark' : 'light')}
              className="demo-btn"
            >
              切换并保存主题
            </button>
          </div>
        </div>
      </div>

      <div className="hooks-info">
        <h3>🔍 Hooks 知识点</h3>
        <div className="info-grid">
          <div className="info-item">
            <h4>useState</h4>
            <p>管理组件的状态数据</p>
          </div>
          <div className="info-item">
            <h4>useEffect</h4>
            <p>处理副作用，如API调用、定时器等</p>
          </div>
          <div className="info-item">
            <h4>useCallback</h4>
            <p>缓存函数，避免不必要的重新创建</p>
          </div>
          <div className="info-item">
            <h4>useMemo</h4>
            <p>缓存计算结果，优化性能</p>
          </div>
          <div className="info-item">
            <h4>useRef</h4>
            <p>访问DOM元素或保存可变值</p>
          </div>
          <div className="info-item">
            <h4>自定义Hook</h4>
            <p>封装可复用的状态逻辑</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HooksDemo;
