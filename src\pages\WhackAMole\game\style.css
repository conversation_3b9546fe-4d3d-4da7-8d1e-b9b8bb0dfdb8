/* 打地鼠游戏样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Nunito', sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

.game-container {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(20px);
    border-radius: 25px;
    padding: 30px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 800px;
    width: 100%;
    position: relative;
}

/* 游戏头部 */
.game-header {
    text-align: center;
    margin-bottom: 30px;
}

.game-title {
    font-family: 'Fredoka One', cursive;
    font-size: 2.5rem;
    color: #fff;
    margin-bottom: 20px;
    text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.game-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    flex-wrap: wrap;
}

.stat-item {
    background: rgba(255, 255, 255, 0.15);
    padding: 15px 25px;
    border-radius: 15px;
    text-align: center;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
}

.stat-item:hover {
    transform: translateY(-3px);
}

.stat-label {
    display: block;
    color: rgba(255, 255, 255, 0.8);
    font-size: 0.9rem;
    margin-bottom: 5px;
}

.stat-value {
    display: block;
    color: #fff;
    font-size: 1.8rem;
    font-weight: 700;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

/* 游戏区域 */
.game-area {
    margin-bottom: 30px;
}

.game-board {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    max-width: 600px;
    margin: 0 auto;
}

/* 地鼠洞穴 */
.hole {
    width: 120px;
    height: 120px;
    background: radial-gradient(circle, #8B4513 0%, #654321 50%, #3E2723 100%);
    border-radius: 50%;
    position: relative;
    overflow: hidden;
    box-shadow: 
        inset 0 10px 20px rgba(0, 0, 0, 0.5),
        0 5px 15px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: transform 0.1s ease;
}

.hole:active {
    transform: scale(0.95);
}

.hole::before {
    content: '';
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    bottom: 10px;
    background: radial-gradient(circle, #2E1A0A 0%, #1A0F06 100%);
    border-radius: 50%;
    box-shadow: inset 0 5px 10px rgba(0, 0, 0, 0.8);
}

/* 地鼠 */
.mole {
    position: absolute;
    bottom: -80px;
    left: 50%;
    transform: translateX(-50%);
    transition: bottom 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    cursor: pointer;
    z-index: 10;
}

.mole.show {
    bottom: 20px;
}

.mole.hit {
    animation: hitAnimation 0.3s ease-out;
}

@keyframes hitAnimation {
    0% { transform: translateX(-50%) scale(1) rotate(0deg); }
    50% { transform: translateX(-50%) scale(1.2) rotate(10deg); }
    100% { transform: translateX(-50%) scale(0.8) rotate(-5deg); }
}

.mole-body {
    width: 60px;
    height: 80px;
    background: linear-gradient(145deg, #8B4513 0%, #A0522D 50%, #8B4513 100%);
    border-radius: 30px 30px 15px 15px;
    position: relative;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.mole-face {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 40px;
}

.mole-eyes {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
}

.eye {
    width: 8px;
    height: 8px;
    background: #000;
    border-radius: 50%;
    position: relative;
}

.eye::after {
    content: '';
    position: absolute;
    top: 1px;
    left: 1px;
    width: 3px;
    height: 3px;
    background: #fff;
    border-radius: 50%;
}

.mole-nose {
    width: 6px;
    height: 4px;
    background: #FF69B4;
    border-radius: 50%;
    margin: 0 auto 5px;
}

.mole-mouth {
    width: 12px;
    height: 6px;
    border: 2px solid #000;
    border-top: none;
    border-radius: 0 0 12px 12px;
    margin: 0 auto;
}

/* 难度选择器 */
.difficulty-selector {
    margin-bottom: 30px;
    text-align: center;
}

.difficulty-title {
    color: #fff;
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.difficulty-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    max-width: 900px;
    margin: 0 auto;
}

.difficulty-btn {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    color: #fff;
    position: relative;
    overflow: hidden;
}

.difficulty-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.difficulty-btn:hover::before {
    left: 100%;
}

.difficulty-btn:hover {
    transform: translateY(-3px);
    border-color: rgba(255, 255, 255, 0.4);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

.difficulty-btn.active {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border-color: #4CAF50;
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.difficulty-btn.active:hover {
    background: linear-gradient(45deg, #45a049, #3d8b40);
}

.difficulty-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: block;
}

.difficulty-name {
    font-size: 1.2rem;
    font-weight: 700;
    margin-bottom: 8px;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.difficulty-desc {
    font-size: 0.9rem;
    color: rgba(255, 255, 255, 0.8);
    line-height: 1.4;
}

.difficulty-btn.active .difficulty-desc {
    color: rgba(255, 255, 255, 0.9);
}

/* 游戏控制按钮 */
.game-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    margin-bottom: 30px;
}

.btn {
    padding: 12px 24px;
    border: none;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.btn-primary {
    background: linear-gradient(45deg, #4CAF50, #45a049);
    color: white;
    box-shadow: 0 4px 15px rgba(76, 175, 80, 0.4);
}

.btn-primary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(76, 175, 80, 0.6);
}

.btn-secondary {
    background: linear-gradient(45deg, #2196F3, #1976D2);
    color: white;
    box-shadow: 0 4px 15px rgba(33, 150, 243, 0.4);
}

.btn-secondary:hover:not(:disabled) {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(33, 150, 243, 0.6);
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none !important;
}

/* 游戏结束弹窗 */
.game-over-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
}

.game-over-modal.show {
    display: flex;
    animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.modal-content {
    background: linear-gradient(145deg, #667eea, #764ba2);
    padding: 40px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 1px solid rgba(255, 255, 255, 0.2);
    max-width: 400px;
    width: 90%;
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.modal-content h2 {
    color: #fff;
    margin-bottom: 20px;
    font-size: 2rem;
}

.final-stats {
    margin-bottom: 30px;
}

.final-stats p {
    color: #fff;
    font-size: 1.2rem;
    margin-bottom: 10px;
}

.final-stats span {
    font-weight: 700;
    color: #FFD700;
}

.modal-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

/* 粒子效果 */
.particles-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 999;
}

.particle {
    position: absolute;
    width: 6px;
    height: 6px;
    background: #FFD700;
    border-radius: 50%;
    animation: particleFloat 1s ease-out forwards;
}

@keyframes particleFloat {
    0% {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
    100% {
        opacity: 0;
        transform: translateY(-100px) scale(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .game-container {
        padding: 20px;
        margin: 10px;
    }

    .game-title {
        font-size: 2rem;
    }

    .game-board {
        gap: 15px;
    }

    .hole {
        width: 100px;
        height: 100px;
    }

    .game-stats {
        gap: 15px;
    }

    .stat-item {
        padding: 10px 15px;
    }

    .difficulty-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }

    .difficulty-btn {
        padding: 15px;
    }

    .difficulty-icon {
        font-size: 2rem;
        margin-bottom: 8px;
    }

    .difficulty-name {
        font-size: 1rem;
        margin-bottom: 5px;
    }

    .difficulty-desc {
        font-size: 0.8rem;
    }
}

@media (max-width: 480px) {
    .difficulty-buttons {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .difficulty-btn {
        padding: 12px;
    }

    .difficulty-title {
        font-size: 1.3rem;
    }

    .game-controls {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 200px;
        margin-bottom: 10px;
    }
}
