import React from 'react';
import { Link, useLocation } from 'react-router';
import { RouteUtils } from '../../routes/routeUtils';
import './Breadcrumb.css';

const Breadcrumb: React.FC = () => {
  const location = useLocation();
  const breadcrumbs = RouteUtils.getBreadcrumbs(location.pathname);

  if (breadcrumbs.length <= 1) {
    return null; // 首页不显示面包屑
  }

  return (
    <nav className="breadcrumb">
      <ol className="breadcrumb-list">
        {breadcrumbs.map((item, index) => (
          <li key={item.path} className="breadcrumb-item">
            {index < breadcrumbs.length - 1 ? (
              <>
                <Link to={item.path} className="breadcrumb-link">
                  {item.name}
                </Link>
                <span className="breadcrumb-separator">›</span>
              </>
            ) : (
              <span className="breadcrumb-current">{item.name}</span>
            )}
          </li>
        ))}
      </ol>
    </nav>
  );
};

export default Breadcrumb;
