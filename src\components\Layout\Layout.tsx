import React, { useState } from 'react';
import { Outlet, Link, useLocation } from 'react-router';
import { getMenuItems } from '../../routes/routeConfig';
import { RouteUtils } from '../../routes/routeUtils';
import Breadcrumb from '../Breadcrumb/Breadcrumb';
import './Layout.css';

const Layout: React.FC = () => {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const location = useLocation();

  // 从路由配置获取菜单项
  const menuItems = getMenuItems();

  const toggleSidebar = () => {
    setIsCollapsed(!isCollapsed);
  };

  const getCurrentPageInfo = () => {
    const currentRoute = RouteUtils.getRouteByPath(location.pathname);
    return currentRoute || { name: '未知页面', description: '' };
  };

  return (
    <div className="layout">
      {/* 侧边栏 */}
      <aside className={`sidebar ${isCollapsed ? 'collapsed' : ''}`}>
        <div className="sidebar-header">
          <div className="logo">
            <span className="logo-icon">🚀</span>
            {!isCollapsed && <span className="logo-text">Tech Lab</span>}
          </div>
          <button className="collapse-btn" onClick={toggleSidebar}>
            {isCollapsed ? '→' : '←'}
          </button>
        </div>

        <nav className="sidebar-nav">
          <ul className="nav-list">
            {menuItems.map((item) => {
              const itemPath = item.path === '' ? '/' : `/${item.path}`;
              const isActive = location.pathname === itemPath;

              return (
                <li key={item.path} className="nav-item">
                  <Link
                    to={itemPath}
                    className={`nav-link ${isActive ? 'active' : ''}`}
                    title={isCollapsed ? item.name : ''}
                  >
                    <span className="nav-icon">{item.icon}</span>
                    {!isCollapsed && (
                      <div className="nav-content">
                        <span className="nav-name">{item.name}</span>
                        <span className="nav-desc">{item.description}</span>
                      </div>
                    )}
                  </Link>
                </li>
              );
            })}
          </ul>
        </nav>

        <div className="sidebar-footer">
          {!isCollapsed && (
            <div className="footer-info">
              <p>React 19 + Router</p>
              <p>技术学习平台</p>
            </div>
          )}
        </div>
      </aside>

      {/* 主内容区 */}
      <main className="main-content">
        {/* 顶部导航栏 */}
        <header className="top-header">
          <div className="header-left">
            <h1 className="page-title">{getCurrentPageInfo().name}</h1>
            <p className="page-description">{getCurrentPageInfo().description}</p>
          </div>
          <div className="header-right">
            <div className="user-info">
              <span className="user-avatar">👨‍💻</span>
              <span className="user-name">开发者</span>
            </div>
          </div>
        </header>

        {/* 页面内容 */}
        <div className="page-content">
          <Breadcrumb />
          <Outlet />
        </div>
      </main>
    </div>
  );
};

export default Layout;
