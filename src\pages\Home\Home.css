/* Home 页面样式 */
.home-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* 欢迎区域 */
.welcome-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  min-height: 500px;
  margin-bottom: 80px;
  position: relative;
  overflow: hidden;
}

.welcome-content {
  flex: 1;
  max-width: 600px;
  z-index: 2;
}

.welcome-title {
  font-size: 3.5rem;
  font-weight: 800;
  color: #2c3e50;
  margin-bottom: 20px;
  line-height: 1.2;
}

.highlight {
  background: linear-gradient(45deg, #3498db, #e74c3c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.welcome-subtitle {
  font-size: 1.5rem;
  color: #7f8c8d;
  margin-bottom: 20px;
  font-weight: 600;
}

.welcome-description {
  font-size: 1.1rem;
  color: #5a6c7d;
  line-height: 1.8;
  margin-bottom: 40px;
}

.welcome-actions {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.cta-button {
  padding: 15px 30px;
  border-radius: 50px;
  text-decoration: none;
  font-weight: 600;
  font-size: 1.1rem;
  transition: all 0.3s ease;
  display: inline-flex;
  align-items: center;
  gap: 8px;
}

.cta-button.primary {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
}

.cta-button.primary:hover {
  transform: translateY(-3px);
  box-shadow: 0 8px 25px rgba(52, 152, 219, 0.4);
}

.cta-button.secondary {
  background: white;
  color: #3498db;
  border: 2px solid #3498db;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.cta-button.secondary:hover {
  background: #3498db;
  color: white;
  transform: translateY(-3px);
}

.welcome-visual {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.floating-elements {
  position: relative;
  width: 300px;
  height: 300px;
}

.floating-element {
  position: absolute;
  font-size: 3rem;
  animation: float 6s ease-in-out infinite;
  opacity: 0.8;
}

.floating-element:nth-child(1) {
  top: 10%;
  left: 20%;
  animation-delay: 0s;
}

.floating-element:nth-child(2) {
  top: 20%;
  right: 10%;
  animation-delay: 1s;
}

.floating-element:nth-child(3) {
  bottom: 30%;
  left: 10%;
  animation-delay: 2s;
}

.floating-element:nth-child(4) {
  bottom: 10%;
  right: 20%;
  animation-delay: 3s;
}

.floating-element:nth-child(5) {
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  animation-delay: 4s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

/* 统计数据区域 */
.stats-section {
  margin-bottom: 80px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 30px;
}

.stat-card {
  background: white;
  padding: 30px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border: 1px solid #f0f0f0;
}

.stat-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-icon {
  font-size: 3rem;
  margin-bottom: 15px;
}

.stat-value {
  font-size: 2.5rem;
  font-weight: 800;
  color: #2c3e50;
  margin-bottom: 10px;
}

.stat-label {
  font-size: 1.1rem;
  color: #7f8c8d;
  font-weight: 600;
}

/* 技术模块区域 */
.tech-section {
  margin-bottom: 80px;
}

.section-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 15px;
}

.section-subtitle {
  font-size: 1.2rem;
  color: #7f8c8d;
  text-align: center;
  margin-bottom: 50px;
}

.tech-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 30px;
}

.tech-card {
  background: white;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  text-decoration: none;
  color: inherit;
  border: 1px solid #f0f0f0;
}

.tech-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  text-decoration: none;
  color: inherit;
}

.card-header {
  padding: 30px;
  color: white;
  position: relative;
  overflow: hidden;
}

.card-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tech-card:hover .card-header::before {
  opacity: 1;
}

.card-icon {
  font-size: 3rem;
  margin-bottom: 15px;
  display: block;
}

.card-title {
  font-size: 1.5rem;
  font-weight: 700;
  margin: 0;
}

.card-body {
  padding: 25px 30px;
}

.card-description {
  font-size: 1rem;
  color: #5a6c7d;
  line-height: 1.6;
  margin-bottom: 20px;
}

.card-features {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.95rem;
  color: #6c757d;
}

.feature-dot {
  color: #3498db;
  font-weight: bold;
  margin-right: 10px;
}

.card-footer {
  padding: 20px 30px;
  border-top: 1px solid #f0f0f0;
  background: #f8f9fa;
}

.learn-more {
  color: #3498db;
  font-weight: 600;
  font-size: 1rem;
  transition: all 0.3s ease;
}

.tech-card:hover .learn-more {
  color: #2980b9;
  transform: translateX(5px);
}

/* 技术栈区域 */
.tech-stack-section {
  margin-bottom: 50px;
}

.tech-stack {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 30px;
  margin-top: 40px;
}

.tech-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  min-width: 120px;
}

.tech-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.tech-logo {
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.tech-name {
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    text-align: center;
    min-height: auto;
    padding: 40px 0;
  }

  .welcome-title {
    font-size: 2.5rem;
  }

  .welcome-visual {
    margin-top: 40px;
  }

  .floating-elements {
    width: 250px;
    height: 250px;
  }

  .tech-grid {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .tech-stack {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .welcome-title {
    font-size: 2rem;
  }

  .section-title {
    font-size: 2rem;
  }

  .stats-grid {
    grid-template-columns: 1fr;
  }

  .cta-button {
    padding: 12px 24px;
    font-size: 1rem;
  }

  .card-header {
    padding: 20px;
  }

  .card-body {
    padding: 20px;
  }
}
