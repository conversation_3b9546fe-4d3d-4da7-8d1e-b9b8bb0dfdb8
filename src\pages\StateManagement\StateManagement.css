.state-management {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
}

.page-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 1.2rem;
  color: #7f8c8d;
}

.management-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 30px;
  margin-bottom: 50px;
}

.management-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.management-card:hover {
  transform: translateY(-5px);
}

.management-card h3 {
  color: #3498db;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.user-profile, .counter-reducer {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.user-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 10px;
  border-left: 4px solid #27ae60;
}

.user-info p {
  margin: 5px 0;
  color: #2c3e50;
}

.login-section {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #e74c3c;
}

.demo-btn {
  background: linear-gradient(45deg, #3498db, #2980b9);
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 1rem;
  transition: all 0.3s ease;
  margin: 5px;
}

.demo-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(52, 152, 219, 0.3);
}

.demo-btn.logout {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.demo-btn.login {
  background: linear-gradient(45deg, #27ae60, #229954);
}

.counter-display {
  text-align: center;
  margin: 20px 0;
}

.count-value {
  font-size: 3rem;
  font-weight: bold;
  color: #e74c3c;
  display: block;
}

.counter-controls {
  display: flex;
  gap: 10px;
  justify-content: center;
  flex-wrap: wrap;
}

.history {
  margin-top: 20px;
}

.history h4 {
  color: #2c3e50;
  margin-bottom: 10px;
}

.history-list {
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.history-item {
  background: #3498db;
  color: white;
  padding: 5px 10px;
  border-radius: 15px;
  font-size: 0.9rem;
  font-weight: bold;
}

.concepts-section {
  margin-bottom: 50px;
}

.concepts-section h3 {
  font-size: 2rem;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
}

.concepts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
}

.concept-card {
  background: white;
  padding: 25px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  border-top: 4px solid #3498db;
}

.concept-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.concept-card h4 {
  color: #2c3e50;
  margin-bottom: 15px;
  font-size: 1.2rem;
}

.concept-card p {
  color: #5a6c7d;
  line-height: 1.6;
  margin-bottom: 15px;
}

.concept-card ul {
  list-style: none;
  padding: 0;
}

.concept-card li {
  color: #7f8c8d;
  padding: 5px 0;
  position: relative;
  padding-left: 20px;
}

.concept-card li::before {
  content: '✓';
  color: #27ae60;
  font-weight: bold;
  position: absolute;
  left: 0;
}

.best-practices {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.best-practices h3 {
  font-size: 2rem;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 30px;
}

.practices-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.practice-item {
  padding: 20px;
  background: #f8f9fa;
  border-radius: 10px;
  border-left: 4px solid #f39c12;
  transition: all 0.3s ease;
}

.practice-item:hover {
  background: #e9ecef;
  transform: translateX(5px);
}

.practice-item h4 {
  color: #2c3e50;
  margin-bottom: 10px;
  font-size: 1.1rem;
}

.practice-item p {
  color: #5a6c7d;
  line-height: 1.6;
  margin: 0;
}

@media (max-width: 768px) {
  .management-grid {
    grid-template-columns: 1fr;
  }
  
  .concepts-grid {
    grid-template-columns: 1fr;
  }
  
  .practices-list {
    grid-template-columns: 1fr;
  }
  
  .counter-controls {
    justify-content: center;
  }
  
  .history-list {
    justify-content: center;
  }
  
  .page-header h2 {
    font-size: 2rem;
  }
}
