# 路由系统说明

## 📁 文件结构

```
src/routes/
├── index.tsx           # 路由入口文件
├── routeConfig.tsx     # 路由配置文件
├── routeUtils.ts       # 路由工具类
└── README.md          # 说明文档
```

## 🚀 主要特性

### 1. 集中式路由配置
- 所有路由配置集中在 `routeConfig.tsx` 中
- 支持路由元信息（meta）配置
- 支持懒加载（Code Splitting）

### 2. 路由工具类
- 提供丰富的路由操作方法
- 支持面包屑导航生成
- 支持路由搜索和验证

### 3. 路由守卫
- 自动设置页面标题
- 支持权限验证（可扩展）
- 统一的路由拦截处理

### 4. 组件懒加载
- 使用 React.lazy 实现代码分割
- 自定义加载组件
- 提升应用性能

## 📝 使用方法

### 添加新路由

1. 在 `routeConfig.tsx` 中添加路由配置：

```typescript
{
  path: 'new-page',
  component: lazy(() => import('../pages/NewPage/NewPage')),
  name: '新页面',
  icon: '🆕',
  description: '这是一个新页面',
  meta: {
    title: '新页面 - Tech Lab',
    requireAuth: false,
    keepAlive: true
  }
}
```

2. 创建对应的页面组件：

```typescript
// src/pages/NewPage/NewPage.tsx
import React from 'react';

const NewPage: React.FC = () => {
  return (
    <div>
      <h1>新页面</h1>
    </div>
  );
};

export default NewPage;
```

### 使用路由工具

```typescript
import { RouteUtils } from '../routes/routeUtils';

// 获取当前路由信息
const currentRoute = RouteUtils.getRouteByPath('/dashboard');

// 获取面包屑导航
const breadcrumbs = RouteUtils.getBreadcrumbs('/dashboard');

// 检查路由是否需要认证
const needsAuth = RouteUtils.requiresAuth('/dashboard');

// 搜索路由
const searchResults = RouteUtils.searchRoutes('聊天');
```

## 🔧 配置选项

### RouteItem 接口

```typescript
interface RouteItem {
  path: string;              // 路由路径
  component: React.ComponentType; // 组件
  name: string;              // 显示名称
  icon: string;              // 图标
  description: string;       // 描述
  meta?: {                   // 元信息
    requireAuth?: boolean;   // 是否需要认证
    title?: string;          // 页面标题
    keepAlive?: boolean;     // 是否缓存
  };
}
```

## 🎯 最佳实践

1. **路由命名**：使用 kebab-case 命名路由路径
2. **组件懒加载**：大型组件建议使用懒加载
3. **元信息配置**：合理配置页面标题和权限
4. **工具类使用**：优先使用 RouteUtils 进行路由操作

## 🔄 扩展功能

### 权限控制
可以在 RouteGuard 组件中添加权限检查逻辑：

```typescript
const checkAuth = () => {
  const currentRoute = RouteUtils.getRouteByPath(location.pathname);
  if (currentRoute?.meta?.requireAuth) {
    // 检查用户是否已登录
    // 如果未登录，重定向到登录页
  }
};
```

### 路由缓存
可以根据 meta.keepAlive 配置实现页面缓存功能。

### 动态路由
可以扩展 routeConfig 支持动态路由配置。
