import { routeConfig, RouteItem } from './routeConfig';

/**
 * 路由工具类
 */
export class RouteUtils {
  /**
   * 根据路径获取路由信息
   */
  static getRouteByPath(path: string): RouteItem | undefined {
    return routeConfig.find(route => {
      const routePath = route.path === '' ? '/' : `/${route.path}`;
      return routePath === path;
    });
  }

  /**
   * 获取面包屑导航
   */
  static getBreadcrumbs(path: string): Array<{ name: string; path: string }> {
    const breadcrumbs = [{ name: '首页', path: '/' }];
    
    if (path !== '/') {
      const currentRoute = this.getRouteByPath(path);
      if (currentRoute) {
        breadcrumbs.push({
          name: currentRoute.name,
          path: path
        });
      }
    }
    
    return breadcrumbs;
  }

  /**
   * 检查路由是否需要认证
   */
  static requiresAuth(path: string): boolean {
    const route = this.getRouteByPath(path);
    return route?.meta?.requireAuth || false;
  }

  /**
   * 获取路由的页面标题
   */
  static getPageTitle(path: string): string {
    const route = this.getRouteByPath(path);
    return route?.meta?.title || 'Tech Lab - 前端技术学习平台';
  }

  /**
   * 获取所有可访问的路由
   */
  static getAccessibleRoutes(): RouteItem[] {
    // 这里可以根据用户权限过滤路由
    return routeConfig;
  }

  /**
   * 检查路径是否有效
   */
  static isValidPath(path: string): boolean {
    return !!this.getRouteByPath(path);
  }

  /**
   * 获取下一个路由
   */
  static getNextRoute(currentPath: string): RouteItem | undefined {
    const currentIndex = routeConfig.findIndex(route => {
      const routePath = route.path === '' ? '/' : `/${route.path}`;
      return routePath === currentPath;
    });
    
    if (currentIndex !== -1 && currentIndex < routeConfig.length - 1) {
      return routeConfig[currentIndex + 1];
    }
    
    return undefined;
  }

  /**
   * 获取上一个路由
   */
  static getPreviousRoute(currentPath: string): RouteItem | undefined {
    const currentIndex = routeConfig.findIndex(route => {
      const routePath = route.path === '' ? '/' : `/${route.path}`;
      return routePath === currentPath;
    });
    
    if (currentIndex > 0) {
      return routeConfig[currentIndex - 1];
    }
    
    return undefined;
  }

  /**
   * 格式化路由路径
   */
  static formatPath(path: string): string {
    return path === '' ? '/' : `/${path}`;
  }

  /**
   * 搜索路由
   */
  static searchRoutes(keyword: string): RouteItem[] {
    const lowerKeyword = keyword.toLowerCase();
    return routeConfig.filter(route => 
      route.name.toLowerCase().includes(lowerKeyword) ||
      route.description.toLowerCase().includes(lowerKeyword)
    );
  }
}
