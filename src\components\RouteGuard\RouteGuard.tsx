import React, { useEffect } from 'react';
import { useLocation } from 'react-router';
import { RouteUtils } from '../../routes/routeUtils';

interface RouteGuardProps {
  children: React.ReactNode;
}

const RouteGuard: React.FC<RouteGuardProps> = ({ children }) => {
  const location = useLocation();

  useEffect(() => {
    // 根据路由配置设置页面标题
    document.title = RouteUtils.getPageTitle(location.pathname);
  }, [location.pathname]);

  // 这里可以添加权限检查逻辑
  // const checkAuth = () => {
  //   const currentRoute = getRouteByPath(location.pathname);
  //   if (currentRoute?.meta?.requireAuth) {
  //     // 检查用户是否已登录
  //     // 如果未登录，重定向到登录页
  //   }
  // };

  return <>{children}</>;
};

export default RouteGuard;
