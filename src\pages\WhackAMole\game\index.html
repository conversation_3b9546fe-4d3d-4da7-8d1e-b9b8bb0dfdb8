<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>打地鼠游戏 - Whack A Mole</title>
    <link rel="stylesheet" href="style.css">
    <link href="https://fonts.googleapis.com/css2?family=Fredoka+One:wght@400&family=Nunito:wght@400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="game-container">
        <!-- 游戏头部 -->
        <header class="game-header">
            <h1 class="game-title">🔨 打地鼠大作战</h1>
            <div class="game-stats">
                <div class="stat-item">
                    <span class="stat-label">得分</span>
                    <span class="stat-value" id="score">0</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">时间</span>
                    <span class="stat-value" id="time">60</span>
                </div>
                <div class="stat-item">
                    <span class="stat-label">连击</span>
                    <span class="stat-value" id="combo">0</span>
                </div>
            </div>
        </header>

        <!-- 游戏区域 -->
        <main class="game-area">
            <div class="game-board" id="gameBoard">
                <!-- 地鼠洞穴 -->
                <div class="hole" data-index="0">
                    <div class="mole" id="mole-0">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hole" data-index="1">
                    <div class="mole" id="mole-1">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hole" data-index="2">
                    <div class="mole" id="mole-2">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hole" data-index="3">
                    <div class="mole" id="mole-3">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hole" data-index="4">
                    <div class="mole" id="mole-4">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hole" data-index="5">
                    <div class="mole" id="mole-5">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hole" data-index="6">
                    <div class="mole" id="mole-6">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hole" data-index="7">
                    <div class="mole" id="mole-7">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="hole" data-index="8">
                    <div class="mole" id="mole-8">
                        <div class="mole-body">
                            <div class="mole-face">
                                <div class="mole-eyes">
                                    <div class="eye left"></div>
                                    <div class="eye right"></div>
                                </div>
                                <div class="mole-nose"></div>
                                <div class="mole-mouth"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>

        <!-- 难度选择 -->
        <div class="difficulty-selector">
            <h3 class="difficulty-title">选择难度</h3>
            <div class="difficulty-buttons">
                <button class="difficulty-btn active" data-difficulty="easy">
                    <div class="difficulty-icon">🐌</div>
                    <div class="difficulty-name">简单</div>
                    <div class="difficulty-desc">慢节奏，适合新手</div>
                </button>
                <button class="difficulty-btn" data-difficulty="normal">
                    <div class="difficulty-icon">🐰</div>
                    <div class="difficulty-name">普通</div>
                    <div class="difficulty-desc">标准速度，平衡体验</div>
                </button>
                <button class="difficulty-btn" data-difficulty="hard">
                    <div class="difficulty-icon">🐆</div>
                    <div class="difficulty-name">困难</div>
                    <div class="difficulty-desc">快节奏，高手挑战</div>
                </button>
                <button class="difficulty-btn" data-difficulty="expert">
                    <div class="difficulty-icon">🚀</div>
                    <div class="difficulty-name">专家</div>
                    <div class="difficulty-desc">极限速度，终极挑战</div>
                </button>
            </div>
        </div>

        <!-- 游戏控制 -->
        <div class="game-controls">
            <button class="btn btn-primary" id="startBtn">开始游戏</button>
            <button class="btn btn-secondary" id="pauseBtn" disabled>暂停</button>
            <button class="btn btn-secondary" id="resetBtn">重置</button>
        </div>

        <!-- 游戏结束弹窗 -->
        <div class="game-over-modal" id="gameOverModal">
            <div class="modal-content">
                <h2>🎉 游戏结束！</h2>
                <div class="final-stats">
                    <p>游戏难度: <span id="finalDifficulty">简单</span></p>
                    <p>最终得分: <span id="finalScore">0</span></p>
                    <p>最高连击: <span id="maxCombo">0</span></p>
                    <p>命中率: <span id="accuracy">0%</span></p>
                </div>
                <div class="modal-buttons">
                    <button class="btn btn-primary" id="playAgainBtn">再玩一次</button>
                    <button class="btn btn-secondary" id="closeModalBtn">关闭</button>
                </div>
            </div>
        </div>

        <!-- 粒子效果容器 -->
        <div class="particles-container" id="particlesContainer"></div>
    </div>

    <script src="script.js"></script>
</body>
</html>
