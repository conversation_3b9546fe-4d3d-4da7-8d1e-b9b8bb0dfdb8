import React, { useEffect, useState, useRef } from 'react';
import styles from './params.module.css';

interface Message {
  id: string;
  text: string;
  timestamp: Date;
  type: 'sent' | 'received';
}

const ParamsA = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isConnected, setIsConnected] = useState(false);
  const [messageCount, setMessageCount] = useState(0);
  const [connectionTime, setConnectionTime] = useState<Date | null>(null);
  const socketRef = useRef<WebSocket | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  useEffect(() => {
    const connectWebSocket = () => {
      try {
        socketRef.current = new WebSocket('ws://192.168.110.179:8082/ws');

        socketRef.current.onopen = () => {
          console.log('WebSocket连接已建立');
          setIsConnected(true);
          setConnectionTime(new Date());

          // 添加系统消息
          const systemMessage: Message = {
            id: Date.now().toString(),
            text: '🎉 连接成功！欢迎使用实时聊天',
            timestamp: new Date(),
            type: 'received'
          };
          setMessages(prev => [...prev, systemMessage]);
        };

        socketRef.current.onmessage = (event) => {
          console.log('接收消息了event', event);

          const newMessage: Message = {
            id: Date.now().toString(),
            text: event.data,
            timestamp: new Date(),
            type: 'received'
          };

          setMessages(prev => [...prev, newMessage]);
          setMessageCount(prev => prev + 1);
        };

        socketRef.current.onclose = () => {
          console.log('WebSocket连接已关闭');
          setIsConnected(false);
          setConnectionTime(null);

          // 添加断开连接消息
          const systemMessage: Message = {
            id: Date.now().toString(),
            text: '❌ 连接已断开',
            timestamp: new Date(),
            type: 'received'
          };
          setMessages(prev => [...prev, systemMessage]);
        };

        socketRef.current.onerror = (error) => {
          console.error('WebSocket错误:', error);
          setIsConnected(false);
        };

      } catch (error) {
        console.error('连接失败:', error);
        setIsConnected(false);
      }
    };

    connectWebSocket();

    // 清理函数
    return () => {
      if (socketRef.current) {
        socketRef.current.close();
      }
    };
  }, []);

  const sendMessage = () => {
    if (!inputValue.trim()) return;

    if (socketRef.current && socketRef.current.readyState === WebSocket.OPEN) {
      // 添加发送的消息到列表
      const sentMessage: Message = {
        id: Date.now().toString(),
        text: inputValue,
        timestamp: new Date(),
        type: 'sent'
      };

      setMessages(prev => [...prev, sentMessage]);
      socketRef.current.send(inputValue);
      setInputValue('');
      setMessageCount(prev => prev + 1);
    } else {
      console.error('WebSocket未连接');
      // 添加错误消息
      const errorMessage: Message = {
        id: Date.now().toString(),
        text: '⚠️ 发送失败：连接已断开',
        timestamp: new Date(),
        type: 'received'
      };
      setMessages(prev => [...prev, errorMessage]);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const clearMessages = () => {
    setMessages([]);
    setMessageCount(0);
  };

  const formatTime = (date: Date) => {
    return date.toLocaleTimeString('zh-CN', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit'
    });
  };

  const getConnectionDuration = () => {
    if (!connectionTime) return '未连接';
    const now = new Date();
    const diff = Math.floor((now.getTime() - connectionTime.getTime()) / 1000);
    const minutes = Math.floor(diff / 60);
    const seconds = diff % 60;
    return `${minutes}:${seconds.toString().padStart(2, '0')}`;
  };

  return (
    <div className={styles.chatContainer}>
      {/* 聊天头部 */}
      <div className={styles.chatHeader}>
        <div className={`${styles.statusIndicator} ${!isConnected ? styles.disconnected : ''}`}></div>
        <h3 className={styles.chatTitle}>实时聊天室</h3>
        <div className={styles.chatStats}>
          <div>消息: {messageCount}</div>
          <div>连接时长: {getConnectionDuration()}</div>
        </div>
      </div>

      {/* 消息容器 */}
      <div className={styles.messagesContainer}>
        {messages.length === 0 ? (
          <div style={{
            textAlign: 'center',
            color: 'rgba(255, 255, 255, 0.6)',
            marginTop: '50px',
            fontSize: '1.1rem'
          }}>
            💬 开始你的第一条消息吧！
          </div>
        ) : (
          messages.map((msg) => (
            <div key={msg.id} className={`${styles.messageItem} ${styles[msg.type]}`}>
              <p className={styles.messageText}>{msg.text}</p>
              <div className={styles.messageTime}>{formatTime(msg.timestamp)}</div>
            </div>
          ))
        )}
        <div ref={messagesEndRef} />
      </div>

      {/* 输入容器 */}
      <div className={styles.inputContainer}>
        <input
          type="text"
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          onKeyPress={handleKeyPress}
          placeholder={isConnected ? "输入消息..." : "等待连接..."}
          className={styles.messageInput}
          disabled={!isConnected}
        />
        <button
          onClick={sendMessage}
          disabled={!isConnected || !inputValue.trim()}
          className={styles.sendButton}
          title="发送消息 (Enter)"
        >
          ➤
        </button>
        <button
          onClick={clearMessages}
          className={styles.sendButton}
          style={{ background: 'linear-gradient(45deg, #ef4444, #dc2626)' }}
          title="清空消息"
        >
          🗑️
        </button>
      </div>
    </div>
  );
};

export default ParamsA;