import React, { Suspense } from 'react';
import { Routes, Route } from 'react-router';
import { routeConfig } from './routeConfig';
import Layout from '../components/Layout/Layout';
import LoadingSpinner from '../components/LoadingSpinner/LoadingSpinner';
import RouteGuard from '../components/RouteGuard/RouteGuard';

const AppRoutes: React.FC = () => {
  return (
    <RouteGuard>
      <Suspense fallback={<LoadingSpinner />}>
        <Routes>
          <Route path="/" element={<Layout />}>
            {routeConfig.map((route) => (
              <Route
                key={route.path}
                path={route.path}
                element={
                  <Suspense fallback={<LoadingSpinner />}>
                    <route.component />
                  </Suspense>
                }
              />
            ))}
          </Route>
        </Routes>
      </Suspense>
    </RouteGuard>
  );
};

export default AppRoutes;
