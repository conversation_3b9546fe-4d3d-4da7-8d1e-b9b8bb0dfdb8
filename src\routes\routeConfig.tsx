import React, { lazy } from 'react';

// 懒加载组件
const Home = lazy(() => import('../pages/Home/Home'));
const WebSocketChat = lazy(() => import('../pages/WebSocketChat/WebSocketChat'));
const Dashboard = lazy(() => import('../pages/Dashboard/Dashboard'));
const WhackAMole = lazy(() => import('../pages/WhackAMole/WhackAMole'));
const HooksDemo = lazy(() => import('../pages/HooksDemo/HooksDemo'));
const StateManagement = lazy(() => import('../pages/StateManagement/StateManagement'));
const ZustandDemo = lazy(() => import('../pages/ZustandDemo/ZustandDemo'));

// 临时组件，用于未完成的页面
const ComingSoon: React.FC<{ title: string }> = ({ title }) => (
  <div style={{
    display: 'flex',
    flexDirection: 'column',
    alignItems: 'center',
    justifyContent: 'center',
    height: '400px',
    background: 'white',
    borderRadius: '15px',
    boxShadow: '0 5px 15px rgba(0,0,0,0.1)',
    margin: '20px'
  }}>
    <h2 style={{ color: '#3498db', marginBottom: '20px' }}>🚧 {title}</h2>
    <p style={{ color: '#7f8c8d', fontSize: '1.2rem' }}>功能开发中，敬请期待...</p>
    <div style={{ fontSize: '4rem', marginTop: '20px' }}>⏳</div>
  </div>
);

// 路由配置接口
export interface RouteItem {
  path: string;
  component: React.ComponentType;
  name: string;
  icon: string;
  description: string;
  meta?: {
    requireAuth?: boolean;
    title?: string;
    keepAlive?: boolean;
  };
}

// 路由配置数组
export const routeConfig: RouteItem[] = [
  {
    path: '',
    component: Home,
    name: '首页',
    icon: '🏠',
    description: '项目概览',
    meta: {
      title: '首页 - Tech Lab',
      keepAlive: true
    }
  },
  {
    path: 'websocket-chat',
    component: WebSocketChat,
    name: 'WebSocket聊天',
    icon: '💬',
    description: '实时通信技术',
    meta: {
      title: 'WebSocket聊天 - Tech Lab'
    }
  },
  {
    path: 'dashboard',
    component: Dashboard,
    name: '数据仪表板',
    icon: '📊',
    description: '数据可视化',
    meta: {
      title: '数据仪表板 - Tech Lab'
    }
  },
  {
    path: 'whack-a-mole',
    component: WhackAMole,
    name: '打地鼠游戏',
    icon: '🔨',
    description: '游戏开发',
    meta: {
      title: '打地鼠游戏 - Tech Lab'
    }
  },
  {
    path: 'hooks-demo',
    component: HooksDemo,
    name: 'React Hooks',
    icon: '⚛️',
    description: 'Hooks实践',
    meta: {
      title: 'React Hooks - Tech Lab'
    }
  },
  {
    path: 'state-management',
    component: StateManagement,
    name: '状态管理',
    icon: '🗃️',
    description: 'Context/Redux',
    meta: {
      title: '状态管理 - Tech Lab'
    }
  },
  {
    path: 'zustand-demo',
    component: ZustandDemo,
    name: 'Zustand状态管理',
    icon: '🐻',
    description: 'Zustand实践',
    meta: {
      title: 'Zustand状态管理 - Tech Lab'
    }
  },
  {
    path: 'animation-demo',
    component: () => <ComingSoon title="动画效果" />,
    name: '动画效果',
    icon: '✨',
    description: 'CSS/JS动画',
    meta: {
      title: '动画效果 - Tech Lab'
    }
  },
  {
    path: 'api-integration',
    component: () => <ComingSoon title="API集成" />,
    name: 'API集成',
    icon: '🌐',
    description: 'HTTP请求处理',
    meta: {
      title: 'API集成 - Tech Lab'
    }
  }
];

// 根据路径获取路由信息
export const getRouteByPath = (path: string): RouteItem | undefined => {
  return routeConfig.find(route => `/${route.path}` === path || (route.path === '' && path === '/'));
};

// 获取所有菜单项（用于导航）
export const getMenuItems = (): RouteItem[] => {
  return routeConfig;
};
