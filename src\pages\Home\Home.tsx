import React from 'react';
import { Link } from 'react-router';
import './Home.css';

interface TechCard {
  title: string;
  description: string;
  icon: string;
  path: string;
  color: string;
  features: string[];
}

const Home: React.FC = () => {
  const techCards: TechCard[] = [
    {
      title: 'WebSocket 实时通信',
      description: '学习WebSocket技术，实现实时聊天功能',
      icon: '💬',
      path: '/websocket-chat',
      color: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)',
      features: ['实时消息', '连接状态', '消息历史', '连击系统']
    },
    {
      title: '数据仪表板',
      description: '数据可视化和系统监控面板',
      icon: '📊',
      path: '/dashboard',
      color: 'linear-gradient(135deg, #1e3c72 0%, #2a5298 100%)',
      features: ['实时数据', '图表展示', '系统监控', '响应式设计']
    },
    {
      title: '打地鼠游戏',
      description: '游戏开发实践，包含动画和音效',
      icon: '🔨',
      path: '/whack-a-mole',
      color: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
      features: ['游戏逻辑', '动画效果', '音效系统', '难度选择']
    },
    {
      title: 'React Hooks',
      description: '深入学习React Hooks的使用',
      icon: '⚛️',
      path: '/hooks-demo',
      color: 'linear-gradient(135deg, #4facfe 0%, #00f2fe 100%)',
      features: ['useState', 'useEffect', '自定义Hook', '性能优化']
    },
    {
      title: '状态管理',
      description: 'Context API和状态管理模式',
      icon: '🗃️',
      path: '/state-management',
      color: 'linear-gradient(135deg, #43e97b 0%, #38f9d7 100%)',
      features: ['Context API', 'useReducer', '状态提升', '数据流']
    },
    {
      title: '动画效果',
      description: 'CSS和JavaScript动画实现',
      icon: '✨',
      path: '/animation-demo',
      color: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)',
      features: ['CSS动画', 'JS动画', '过渡效果', '交互动画']
    },
    {
      title: 'API 集成',
      description: 'HTTP请求和数据处理',
      icon: '🌐',
      path: '/api-integration',
      color: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)',
      features: ['Fetch API', '错误处理', '加载状态', '数据缓存']
    }
  ];

  const stats = [
    { label: '技术模块', value: '7+', icon: '🛠️' },
    { label: '实践项目', value: '10+', icon: '🚀' },
    { label: '代码示例', value: '50+', icon: '💻' },
    { label: '学习时长', value: '∞', icon: '⏰' }
  ];

  return (
    <div className="home-container">
      {/* 欢迎区域 */}
      <section className="welcome-section">
        <div className="welcome-content">
          <h1 className="welcome-title">
            欢迎来到 <span className="highlight">Tech Lab</span>
          </h1>
          <p className="welcome-subtitle">
            基于 React 19 的现代前端技术学习平台
          </p>
          <p className="welcome-description">
            在这里你可以学习和实践各种前端技术，包括React Hooks、状态管理、
            WebSocket通信、游戏开发、动画效果等。每个模块都包含完整的代码示例和详细说明。
          </p>
          <div className="welcome-actions">
            <Link to="/websocket-chat" className="cta-button primary">
              开始学习 🚀
            </Link>
            <Link to="/whack-a-mole" className="cta-button secondary">
              体验游戏 🎮
            </Link>
          </div>
        </div>
        <div className="welcome-visual">
          <div className="floating-elements">
            <div className="floating-element">⚛️</div>
            <div className="floating-element">🚀</div>
            <div className="floating-element">💻</div>
            <div className="floating-element">🎨</div>
            <div className="floating-element">🔧</div>
          </div>
        </div>
      </section>

      {/* 统计数据 */}
      <section className="stats-section">
        <div className="stats-grid">
          {stats.map((stat, index) => (
            <div key={index} className="stat-card">
              <div className="stat-icon">{stat.icon}</div>
              <div className="stat-value">{stat.value}</div>
              <div className="stat-label">{stat.label}</div>
            </div>
          ))}
        </div>
      </section>

      {/* 技术模块卡片 */}
      <section className="tech-section">
        <h2 className="section-title">技术模块</h2>
        <p className="section-subtitle">选择你感兴趣的技术领域开始学习</p>
        
        <div className="tech-grid">
          {techCards.map((card, index) => (
            <Link key={index} to={card.path} className="tech-card">
              <div className="card-header" style={{ background: card.color }}>
                <div className="card-icon">{card.icon}</div>
                <h3 className="card-title">{card.title}</h3>
              </div>
              <div className="card-body">
                <p className="card-description">{card.description}</p>
                <ul className="card-features">
                  {card.features.map((feature, idx) => (
                    <li key={idx} className="feature-item">
                      <span className="feature-dot">•</span>
                      {feature}
                    </li>
                  ))}
                </ul>
              </div>
              <div className="card-footer">
                <span className="learn-more">开始学习 →</span>
              </div>
            </Link>
          ))}
        </div>
      </section>

      {/* 技术栈 */}
      <section className="tech-stack-section">
        <h2 className="section-title">技术栈</h2>
        <div className="tech-stack">
          <div className="tech-item">
            <span className="tech-logo">⚛️</span>
            <span className="tech-name">React 19</span>
          </div>
          <div className="tech-item">
            <span className="tech-logo">🛣️</span>
            <span className="tech-name">React Router</span>
          </div>
          <div className="tech-item">
            <span className="tech-logo">📘</span>
            <span className="tech-name">TypeScript</span>
          </div>
          <div className="tech-item">
            <span className="tech-logo">🎨</span>
            <span className="tech-name">CSS3</span>
          </div>
          <div className="tech-item">
            <span className="tech-logo">🌐</span>
            <span className="tech-name">WebSocket</span>
          </div>
          <div className="tech-item">
            <span className="tech-logo">📊</span>
            <span className="tech-name">Data Viz</span>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Home;
