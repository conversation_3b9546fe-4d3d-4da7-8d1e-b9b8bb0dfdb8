import React from 'react';
import { create } from 'zustand';
import './ZustandDemo.css';

// 定义计数器状态接口
interface CounterState {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
}

// 创建Zustand store
const useCounterStore = create<CounterState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),
}));

// A组件 - 显示计数和操作按钮
const ComponentA: React.FC = () => {
  const { count, increment, decrement, reset } = useCounterStore();

  return (
    <div className="component-card">
      <h3>A组件</h3>
      <div className="counter-display">
        <span className="count-number">{count}</span>
      </div>
      <div className="button-group">
        <button onClick={increment} className="btn btn-primary">增加+1</button>
        <button onClick={decrement} className="btn btn-secondary">减少-1</button>
        <button onClick={reset} className="btn btn-danger">重置</button>
      </div>
    </div>
  );
};

// B组件 - 只显示计数
const ComponentB: React.FC = () => {
  const count = useCounterStore((state) => state.count);

  return (
    <div className="component-card">
      <h3>B组件</h3>
      <div className="count-display">
        <p>当前计数: <strong>{count}</strong></p>
        <p className="count-status">
          {count === 0 && '🔄 初始状态'}
          {count > 0 && '📈 正数'}
          {count < 0 && '📉 负数'}
        </p>
      </div>
    </div>
  );
};

const ZustandDemo: React.FC = () => {
  const count = useCounterStore((state) => state.count);

  return (
    <div className="zustand-demo">
      <div className="demo-header">
        <h2>🐻 Zustand 状态管理</h2>
        <p>学习使用Zustand进行组件间状态传递和管理</p>
      </div>

      <div className="demo-content">
        <div className="intro-section">
          <h3>📚 关于 Zustand</h3>
          <p>
            Zustand 是一个轻量级的状态管理库，提供简单、快速、可扩展的状态管理解决方案。
            它比Redux更简单，比Context API更高效。
          </p>

          <div className="features-grid">
            <div className="feature-card">
              <h4>🚀 轻量级</h4>
              <p>只有2.5kb的体积，无需样板代码</p>
            </div>
            <div className="feature-card">
              <h4>⚡ 高性能</h4>
              <p>基于订阅模式，只更新相关组件</p>
            </div>
            <div className="feature-card">
              <h4>🔧 易使用</h4>
              <p>简单的API，学习成本低</p>
            </div>
            <div className="feature-card">
              <h4>📦 TypeScript</h4>
              <p>完整的TypeScript支持</p>
            </div>
          </div>
        </div>

        {/* 实际测试区域 */}
        <div className="test-section">
          <h3>🧪 Zustand 实战测试</h3>
          <p>下面两个组件共享同一个Zustand状态，观察状态同步效果</p>

          <div className="components-container">
            <ComponentA />
            <ComponentB />
          </div>

          <div className="global-status">
            <h4>全局状态: {count}</h4>
            <p>✨ 两个组件完全独立，但共享同一个状态！</p>
          </div>
        </div>

        <div className="demo-sections">
          <div className="demo-section">
            <h3>📝 代码示例</h3>
            <div className="code-example">
              <h4>1. 创建Store</h4>
              <pre><code>{`import { create } from 'zustand';

interface CounterState {
  count: number;
  increment: () => void;
  decrement: () => void;
  reset: () => void;
}

const useCounterStore = create<CounterState>((set) => ({
  count: 0,
  increment: () => set((state) => ({ count: state.count + 1 })),
  decrement: () => set((state) => ({ count: state.count - 1 })),
  reset: () => set({ count: 0 }),
}));`}</code></pre>
            </div>

            <div className="code-example">
              <h4>2. 在组件中使用</h4>
              <pre><code>{`const ComponentA = () => {
  const { count, increment, decrement, reset } = useCounterStore();

  return (
    <div>
      <p>计数: {count}</p>
      <button onClick={increment}>+1</button>
      <button onClick={decrement}>-1</button>
      <button onClick={reset}>重置</button>
    </div>
  );
};`}</code></pre>
            </div>

            <div className="code-example">
              <h4>3. 选择性订阅</h4>
              <pre><code>{`const ComponentB = () => {
  // 只订阅count变化，不订阅方法
  const count = useCounterStore((state) => state.count);

  return <p>当前计数: {count}</p>;
};`}</code></pre>
            </div>
          </div>

          <div className="demo-section">
            <h3>🎯 Zustand 优势</h3>
            <div className="advantages-grid">
              <div className="advantage-item">
                <h4>🚀 简单易用</h4>
                <p>无需Provider包装，直接使用hook</p>
              </div>
              <div className="advantage-item">
                <h4>⚡ 高性能</h4>
                <p>只有使用状态的组件才会重新渲染</p>
              </div>
              <div className="advantage-item">
                <h4>📦 轻量级</h4>
                <p>体积小，无额外依赖</p>
              </div>
              <div className="advantage-item">
                <h4>🔧 TypeScript</h4>
                <p>完美的TypeScript支持</p>
              </div>
            </div>
          </div>
        </div>

        <div className="getting-started">
          <h3>💡 使用技巧</h3>
          <div className="tips-grid">
            <div className="tip-item">
              <h4>选择性订阅</h4>
              <p>使用选择器函数只订阅需要的状态，避免不必要的重渲染</p>
              <code>const count = useStore(state =&gt; state.count)</code>
            </div>
            <div className="tip-item">
              <h4>状态更新</h4>
              <p>使用set函数更新状态，支持函数式更新</p>
              <code>set(state =&gt; ({'{'} count: state.count + 1 {'}'})</code>
            </div>
            <div className="tip-item">
              <h4>异步操作</h4>
              <p>在action中直接处理异步操作</p>
              <code>fetchData: async () =&gt; {'{'} const data = await api(); set({'{'} data {'}'}); {'}'}</code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ZustandDemo;
