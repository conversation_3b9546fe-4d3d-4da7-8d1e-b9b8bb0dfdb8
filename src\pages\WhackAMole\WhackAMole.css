.whack-a-mole-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.page-header {
  text-align: center;
  margin-bottom: 30px;
}

.page-header h2 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 10px;
}

.page-header p {
  font-size: 1.2rem;
  color: #7f8c8d;
}

.game-container {
  background: white;
  border-radius: 20px;
  padding: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.game-iframe {
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.game-info {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
}

.info-card {
  background: white;
  padding: 30px;
  border-radius: 15px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease;
}

.info-card:hover {
  transform: translateY(-5px);
}

.info-card h3 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 1.3rem;
}

.info-card ul {
  list-style: none;
  padding: 0;
}

.info-card li {
  padding: 8px 0;
  color: #5a6c7d;
  position: relative;
  padding-left: 20px;
}

.info-card li::before {
  content: '•';
  color: #3498db;
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* React游戏组件样式 */
.game-container-react {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  position: relative;
  overflow: hidden;
  margin-bottom: 30px;
}

.game-container-react::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  z-index: 0;
}

.game-container-react > * {
  position: relative;
  z-index: 1;
}

/* 游戏头部 */
.game-header-react {
  text-align: center;
  margin-bottom: 30px;
}

.game-title-react {
  font-size: 2.5rem;
  color: #fff;
  margin-bottom: 20px;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
  40% { transform: translateY(-10px); }
  60% { transform: translateY(-5px); }
}

.game-stats-react {
  display: flex;
  justify-content: center;
  gap: 30px;
  flex-wrap: wrap;
}

.stat-item-react {
  background: rgba(255, 255, 255, 0.15);
  padding: 15px 25px;
  border-radius: 15px;
  text-align: center;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: transform 0.3s ease;
}

.stat-item-react:hover {
  transform: translateY(-3px);
}

.stat-label-react {
  display: block;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
  margin-bottom: 5px;
}

.stat-value-react {
  display: block;
  color: #fff;
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.stat-value-react.combo-highlight {
  color: #FFD700;
  text-shadow: 0 0 10px #FFD700;
  animation: pulse 0.5s ease-in-out;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

/* 游戏区域 */
.game-area-react {
  margin-bottom: 30px;
}

.game-board-react {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  max-width: 600px;
  margin: 0 auto;
}

/* 地鼠洞穴 */
.hole-react {
  width: 120px;
  height: 120px;
  background: radial-gradient(circle, #8B4513 0%, #654321 50%, #3E2723 100%);
  border-radius: 50%;
  position: relative;
  overflow: hidden;
  box-shadow:
    inset 0 10px 20px rgba(0, 0, 0, 0.5),
    0 5px 15px rgba(0, 0, 0, 0.3);
  cursor: pointer;
  transition: transform 0.1s ease;
  justify-self: center;
}

.hole-react:active {
  transform: scale(0.95);
}

.hole-react::before {
  content: '';
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  bottom: 10px;
  background: radial-gradient(circle, #2E1A0A 0%, #1A0F06 100%);
  border-radius: 50%;
  box-shadow: inset 0 5px 10px rgba(0, 0, 0, 0.8);
}

/* 地鼠 */
.mole-react {
  position: absolute;
  bottom: -80px;
  left: 50%;
  transform: translateX(-50%);
  transition: bottom 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  cursor: pointer;
  z-index: 10;
}

.mole-react.show {
  bottom: 20px;
}

.mole-react.hit {
  animation: hitAnimation 0.3s ease-out;
}

@keyframes hitAnimation {
  0% { transform: translateX(-50%) scale(1) rotate(0deg); }
  50% { transform: translateX(-50%) scale(1.2) rotate(10deg); }
  100% { transform: translateX(-50%) scale(0.8) rotate(-5deg); }
}

.mole-body-react {
  width: 60px;
  height: 80px;
  background: linear-gradient(145deg, #8B4513 0%, #A0522D 50%, #8B4513 100%);
  border-radius: 30px 30px 15px 15px;
  position: relative;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
}

.mole-face-react {
  position: absolute;
  top: 10px;
  left: 50%;
  transform: translateX(-50%);
  width: 50px;
  height: 40px;
}

.mole-eyes-react {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.eye-react {
  width: 8px;
  height: 8px;
  background: #000;
  border-radius: 50%;
  position: relative;
}

.eye-react::after {
  content: '';
  position: absolute;
  top: 1px;
  left: 1px;
  width: 3px;
  height: 3px;
  background: #fff;
  border-radius: 50%;
}

.mole-nose-react {
  width: 6px;
  height: 4px;
  background: #000;
  border-radius: 50%;
  margin: 0 auto 4px;
}

.mole-mouth-react {
  width: 12px;
  height: 6px;
  border: 2px solid #000;
  border-top: none;
  border-radius: 0 0 12px 12px;
  margin: 0 auto;
}

@media (max-width: 768px) {
  .page-header h2 {
    font-size: 2rem;
  }

  .game-container {
    padding: 15px;
  }

  .game-iframe {
    height: 600px;
  }

  .info-card {
    padding: 20px;
  }

  .game-container-react {
    padding: 20px;
  }

  .game-title-react {
    font-size: 2rem;
  }

  .game-stats-react {
    gap: 15px;
  }

  .stat-item-react {
    padding: 10px 15px;
  }

  .stat-value-react {
    font-size: 1.5rem;
  }

  .game-board-react {
    gap: 15px;
  }

  .hole-react {
    width: 100px;
    height: 100px;
  }

  .mole-body-react {
    width: 50px;
    height: 70px;
  }
}

/* 难度选择 */
.difficulty-selector-react {
  margin-bottom: 30px;
  text-align: center;
}

.difficulty-title-react {
  color: #fff;
  margin-bottom: 20px;
  font-size: 1.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.difficulty-buttons-react {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  max-width: 800px;
  margin: 0 auto;
}

.difficulty-btn-react {
  background: rgba(255, 255, 255, 0.15);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 15px;
  padding: 15px;
  color: #fff;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  text-align: center;
}

.difficulty-btn-react:hover:not(.disabled) {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-3px);
}

.difficulty-btn-react.active {
  background: rgba(255, 255, 255, 0.3);
  border-color: #FFD700;
  box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.difficulty-btn-react.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.difficulty-icon-react {
  font-size: 2rem;
  margin-bottom: 8px;
}

.difficulty-name-react {
  font-weight: 600;
  margin-bottom: 5px;
  font-size: 1.1rem;
}

.difficulty-desc-react {
  font-size: 0.8rem;
  opacity: 0.8;
}

/* 游戏控制 */
.game-controls-react {
  display: flex;
  justify-content: center;
  gap: 15px;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.btn-react {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 1rem;
  min-width: 120px;
}

.btn-primary-react {
  background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.btn-primary-react:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.btn-secondary-react {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%);
  color: white;
  box-shadow: 0 4px 15px rgba(108, 117, 125, 0.3);
}

.btn-secondary-react:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(108, 117, 125, 0.4);
}

.btn-react:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none !important;
  box-shadow: none !important;
}

/* 游戏结束弹窗 */
.game-over-modal-react {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.game-over-modal-react.show {
  opacity: 1;
  visibility: visible;
}

.modal-content-react {
  background: white;
  border-radius: 20px;
  padding: 40px;
  max-width: 500px;
  width: 90%;
  text-align: center;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  transform: scale(0.8);
  transition: transform 0.3s ease;
}

.game-over-modal-react.show .modal-content-react {
  transform: scale(1);
}

.modal-content-react h2 {
  color: #2c3e50;
  margin-bottom: 20px;
  font-size: 2rem;
}

.final-stats-react {
  margin-bottom: 30px;
}

.final-stats-react p {
  margin-bottom: 10px;
  color: #5a6c7d;
  font-size: 1.1rem;
}

.final-stats-react span {
  color: #2c3e50;
  font-weight: 600;
}

.modal-buttons-react {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

/* 响应式设计增强 */
@media (max-width: 480px) {
  .difficulty-buttons-react {
    grid-template-columns: 1fr 1fr;
  }

  .game-controls-react {
    flex-direction: column;
    align-items: center;
  }

  .btn-react {
    width: 200px;
  }

  .modal-content-react {
    padding: 30px 20px;
  }

  .modal-buttons-react {
    flex-direction: column;
    align-items: center;
  }
}
