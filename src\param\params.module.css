/* 现代化聊天界面样式 */
.chatContainer {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

.chatHeader {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  gap: 15px;
}

.statusIndicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4ade80;
  box-shadow: 0 0 10px rgba(74, 222, 128, 0.5);
  animation: pulse 2s infinite;
}

.statusIndicator.disconnected {
  background: #ef4444;
  box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

.chatTitle {
  color: white;
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.chatStats {
  margin-left: auto;
  color: rgba(255, 255, 255, 0.8);
  font-size: 0.9rem;
}

.messagesContainer {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.messageItem {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 15px;
  padding: 15px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  animation: slideIn 0.3s ease-out;
}

.messageItem.sent {
  background: rgba(74, 222, 128, 0.2);
  border: 1px solid rgba(74, 222, 128, 0.3);
  margin-left: 20%;
}

.messageItem.received {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  margin-right: 20%;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.messageText {
  color: white;
  margin: 0;
  line-height: 1.5;
}

.messageTime {
  color: rgba(255, 255, 255, 0.6);
  font-size: 0.8rem;
  margin-top: 5px;
}

.inputContainer {
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  display: flex;
  gap: 15px;
  align-items: center;
}

.messageInput {
  flex: 1;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 25px;
  padding: 12px 20px;
  color: white;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
}

.messageInput::placeholder {
  color: rgba(255, 255, 255, 0.6);
}

.messageInput:focus {
  border-color: rgba(74, 222, 128, 0.5);
  box-shadow: 0 0 20px rgba(74, 222, 128, 0.2);
}

.sendButton {
  background: linear-gradient(45deg, #4ade80, #22c55e);
  border: none;
  border-radius: 50%;
  width: 50px;
  height: 50px;
  color: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(74, 222, 128, 0.3);
}

.sendButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(74, 222, 128, 0.4);
}

.sendButton:disabled {
  background: rgba(255, 255, 255, 0.2);
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* 仪表板样式 */
.dashboardContainer {
  background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  position: relative;
  overflow: hidden;
}

.dashboardHeader {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  padding: 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.dashboardTitle {
  color: white;
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  text-align: center;
}

.dashboardContent {
  flex: 1;
  padding: 30px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 25px;
  overflow-y: auto;
}

.card {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 25px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s ease;
  cursor: pointer;
}

.card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.3);
}

.cardIcon {
  width: 50px;
  height: 50px;
  background: linear-gradient(45deg, #4ade80, #22c55e);
  border-radius: 15px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 15px;
  font-size: 1.5rem;
}

.cardTitle {
  color: white;
  margin: 0 0 10px 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.cardDescription {
  color: rgba(255, 255, 255, 0.8);
  margin: 0 0 15px 0;
  line-height: 1.5;
}

.cardValue {
  color: #4ade80;
  font-size: 2rem;
  font-weight: 700;
  margin: 0;
}

.progressBar {
  width: 100%;
  height: 8px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  overflow: hidden;
  margin-top: 15px;
}

.progressFill {
  height: 100%;
  background: linear-gradient(45deg, #4ade80, #22c55e);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.actionButton {
  background: linear-gradient(45deg, #3b82f6, #1d4ed8);
  border: none;
  border-radius: 12px;
  padding: 12px 24px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 15px;
  width: 100%;
}

.actionButton:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-top: 15px;
}

.statItem {
  text-align: center;
  padding: 15px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 12px;
}

.statLabel {
  color: rgba(255, 255, 255, 0.7);
  font-size: 0.9rem;
  margin: 0 0 5px 0;
}

.statValue {
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 0;
}