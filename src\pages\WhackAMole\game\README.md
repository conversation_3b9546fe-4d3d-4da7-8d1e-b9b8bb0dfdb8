# 🔨 打地鼠游戏 - Whack A Mole

一个精美的打地鼠小游戏，使用纯HTML、CSS和JavaScript实现。

## 🎮 游戏特色

### 🌟 精美界面
- **现代化设计**: 使用渐变背景和毛玻璃效果
- **可爱地鼠**: 手绘风格的地鼠角色，带有生动的表情
- **流畅动画**: 丰富的CSS动画和过渡效果
- **响应式布局**: 支持各种屏幕尺寸

### 🎯 游戏功能
- **实时计分**: 显示当前得分、剩余时间和连击数
- **连击系统**: 连续击中地鼠可获得额外分数
- **难度递增**: 随着游戏进行，地鼠出现速度越来越快
- **粒子效果**: 击中地鼠时的炫酷粒子动画
- **音效系统**: 击中、未击中和游戏结束的音效

### 🎵 交互体验
- **键盘控制**: 支持空格键开始/暂停游戏
- **游戏统计**: 显示最终得分、最高连击和命中率
- **暂停功能**: 可以随时暂停和继续游戏
- **重新开始**: 一键重置游戏状态

## 🚀 如何运行

1. **直接打开**: 在浏览器中打开 `index.html` 文件
2. **本地服务器**: 使用Live Server或其他本地服务器运行

## 🎮 游戏操作

### 鼠标操作
- **点击地鼠**: 击中地鼠获得分数
- **点击按钮**: 控制游戏开始、暂停、重置

### 键盘操作
- **空格键**: 开始游戏或暂停/继续

## 🏆 游戏规则

### 得分系统
- **基础分数**: 击中地鼠获得10分
- **连击奖励**: 连续击中可获得额外分数 (连击数 × 2)
- **时间限制**: 60秒游戏时间

### 连击系统
- 连续击中地鼠增加连击数
- 未击中或地鼠自动消失会重置连击
- 连击数越高，额外分数越多

### 难度变化
- 游戏开始时地鼠出现较慢
- 随着时间推移，地鼠出现频率增加
- 地鼠显示时间逐渐缩短

## 🛠️ 技术实现

### 前端技术
- **HTML5**: 语义化结构
- **CSS3**: 现代样式和动画
- **JavaScript ES6+**: 游戏逻辑和交互

### 核心功能
- **面向对象编程**: 使用类封装游戏逻辑
- **事件驱动**: 响应用户交互
- **定时器管理**: 控制游戏节奏
- **DOM操作**: 动态更新界面
- **Web Audio API**: 生成游戏音效

### 动画效果
- **CSS动画**: 地鼠出现/消失动画
- **粒子系统**: 击中效果的粒子动画
- **得分动画**: 动态显示得分增加
- **按钮交互**: 悬停和点击效果

## 📱 响应式设计

游戏支持多种设备：
- **桌面端**: 完整功能体验
- **平板端**: 适配中等屏幕
- **手机端**: 优化触摸操作

## 🎨 自定义选项

可以轻松修改的参数：
- **游戏时长**: 修改 `timeLeft` 初始值
- **地鼠速度**: 调整 `moleSpeed` 和 `moleShowTime`
- **得分规则**: 修改基础分数和连击奖励
- **颜色主题**: 更改CSS中的颜色变量
- **地鼠数量**: 增加或减少游戏板上的洞穴

## 🔧 扩展功能建议

### 可添加的功能
1. **等级系统**: 不同难度等级
2. **道具系统**: 特殊地鼠和道具
3. **排行榜**: 本地存储最高分
4. **主题切换**: 多种视觉主题
5. **多人模式**: 竞技对战功能

### 技术优化
1. **性能优化**: 使用requestAnimationFrame
2. **音效增强**: 添加背景音乐
3. **数据持久化**: LocalStorage保存记录
4. **PWA支持**: 离线游戏功能

## 📄 文件结构

```
game/
├── index.html      # 主页面
├── style.css       # 样式文件
├── script.js       # 游戏逻辑
└── README.md       # 说明文档
```

## 🎉 开始游戏

打开 `index.html` 文件，点击"开始游戏"按钮，享受打地鼠的乐趣吧！

---

**祝你游戏愉快！** 🎮✨
